# 工具生成代码转换规范

## 项目概述

**项目名称**: MyTree（果树认养）
**技术栈**: Vue 3.4.21 + uni-app 3.0 + Vite 5.2.8
**支持平台**: H5、微信小程序等多平台
**设计稿基准**: 750px 宽度

## 🎯 核心转换原则

### ⚠️ 最重要原则：样式保持不变 + 代码优秀
- **样式一致性**: 转换后的页面必须与原设计稿在视觉上完全一致
- **布局精确性**: 所有元素的位置、大小、间距必须准确还原
- **颜色准确性**: 所有颜色值必须与设计稿保持一致
- **字体规范性**: 字号、行高、字重必须精确匹配
- **交互保真性**: 所有交互效果和动画必须保持原有体验
- **代码优秀性**: 在保持样式不变的前提下，确保代码结构良好、可维护性强

### 📋 转换任务要求
- **功能样式保真**: 转换代码时要尽可能保证功能和样式不变
- **任务聚焦**: 用户让处理什么，你就处理什么，不要自由发挥去创建相关页面
- **渐进式开发**: 我们一步步来，合理拆分组件
- **组件优先级**: 优先创建子组件，再实现主组件，以降低主组件的复杂度
- **代码质量**: 在保持样式的基础上，追求代码结构清晰、命名规范、逻辑合理

## 🔄 代码转换规范

### 1. 图片资源处理
- **图标文件**: 存放在 `static` 目录下，使用时直接引用路径
- **内容图片**: 使用网络示例图片
- **命名规范**: 普通状态命名为 `tower`，激活状态命名为 `tower-active`





#### Image组件转换规范
```html
<!-- 工具生成 -->
<img src="/path/image.jpg" />

<!-- uni-app转换 -->
<image src="/static/images/image.jpg" mode="aspectFit" />
```

**转换要点**:
- 必须添加 `mode` 属性，确保图片显示效果一致
- 路径统一使用 `/static/` 开头
- 常用mode值: `aspectFit`(保持比例), `aspectFill`(填充), `widthFix`(宽度固定)
- **样式保持**: 确保转换后图片的显示效果与原设计完全一致

### 2. CSS单位转换 - 保持样式精确性

#### 尺寸单位转换规则
```scss
// 750px设计稿 → 1:1精确转换为rpx
// 工具生成: width: 240px
// 转换后: width: 240rpx
// 重要：必须保持数值完全一致，确保样式不变

$design-width: 750px; // 设计稿宽度基准
// 转换公式: 设计稿px值 = uni-app rpx值 (1:1对应)
```

#### 精确单位转换示例
```scss
// 工具生成样式 → uni-app转换（保持数值不变）
.example {
  width: 670px;         → width: 670rpx;
  height: 88px;         → height: 88rpx;
  font-size: 32px;      → font-size: 32rpx;
  margin: 40px;         → margin: 40rpx;
  padding: 20px 16px;   → padding: 20rpx 16rpx;
  border-radius: 326px; → border-radius: 326rpx;
  line-height: 1.5;     → line-height: 1.5; // 无单位保持不变
}
```

#### ⚠️ 单位转换注意事项
- **精确转换**: 所有px值必须1:1转换为rpx，不得随意调整
- **保持比例**: 确保转换后的布局比例与原设计完全一致
- **无单位值**: line-height等无单位值保持不变
- **边框处理**: border宽度也需要转换，如 `1px` → `1rpx`

### 3. HTML标签转换规范

#### 基础标签转换
```html
<!-- 工具生成 → uni-app转换 -->
<div>           → <view>
<span>          → <text>
<p>             → <view> 或 <text>
<img>           → <image>
<input>         → <input> (保持不变)
<button>        → <button> (保持不变)
```

#### 布局容器处理
```html
<!-- 保持原有的CSS类名和样式结构 -->
<!-- 工具生成 -->
<div class="container">
  <div class="header">
    <span class="title">标题</span>
  </div>
</div>

<!-- uni-app转换 -->
<view class="container">
  <view class="header">
    <text class="title">标题</text>
  </view>
</view>
```

### 4. 样式优化规范 - 保持设计一致性

#### Sass变量定义（基于设计稿颜色）
```scss
// 主要颜色变量（严格按照设计稿）
$primary-color: #dd3c29;    // 主色调（参考新增地址页面按钮颜色）
$white-color: #ffffff;      // 白色
$text-gray: #666666;        // 文字灰色
$border-gray: #999999;      // 边框灰色
$background-gray: #f5f5f5;  // 背景灰色

// 字体大小变量（基于750px设计稿）
$font-size-large: 36rpx;    // 大标题
$font-size-medium: 32rpx;   // 中等文字
$font-size-normal: 28rpx;   // 正常文字
$font-size-small: 24rpx;    // 小文字

// 间距变量
$spacing-large: 40rpx;      // 大间距
$spacing-medium: 24rpx;     // 中等间距
$spacing-small: 16rpx;      // 小间距
```

#### 样式组织原则
- **保持原有结构**: 不要随意改变CSS类名和层级关系
- **精确还原**: 所有颜色、字号、间距必须与设计稿一致
- **避免过度优化**: 简单页面优先保持原有样式结构，避免过度拆分

### 5. 交互功能实现

#### 事件处理转换
```html
<!-- 工具生成 -->
<div onclick="handleClick()">点击</div>

<!-- uni-app转换 -->
<view @click="handleClick">点击</view>
```

#### uni-app API集成
```javascript
// 页面跳转
uni.navigateTo({
  url: '/pages/target/target'
})

// 返回上一页
uni.navigateBack()

// 显示提示
uni.showToast({
  title: '操作成功',
  icon: 'success'
})

// 显示模态框
uni.showModal({
  title: '提示',
  content: '确定要执行此操作吗？',
  success: (res) => {
    if (res.confirm) {
      // 用户点击确定
    }
  }
})
```

## ⚠️ 重要注意事项 - 样式保持核心要点

### 1. 顶部导航栏处理
```html
<!-- ❌ 工具生成的顶部导航栏代码需要删除 -->
<div class="header-section">
  <div class="status-bar">...</div>
  <div class="nav-bar">
    <div class="back-button">返回</div>
    <div class="title">页面标题</div>
  </div>
</div>

<!-- ✅ 小程序自带导航栏，通过pages.json配置 -->
```

**处理原则**:
- 删除工具生成的导航栏代码，避免样式冲突
- 使用小程序原生导航栏，确保平台一致性
- 通过pages.json配置标题和样式

**配置方式**:
```json
// pages.json
{
  "path": "pages/login/login",
  "style": {
    "navigationBarTitleText": "登录",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
```

### 2. 字体处理规范
```scss
// ❌ 避免使用自定义字体（可能导致样式不一致）
font-family: 'HarmonyOS Sans SC';

// ✅ 使用系统默认字体（确保跨平台一致性）
// 不设置font-family，让系统自动选择最佳字体
```

### 3. 绝对定位处理建议
```scss
// ❌ 尽量避免使用绝对定位（可能导致布局问题）
position: absolute;
top: 100px;
left: 50px;

// ✅ 优先使用flex布局或相对定位
display: flex;
align-items: center;
justify-content: center;
```

**处理原则**:
- 移除不必要的绝对定位
- 检查对齐布局，确保元素位置准确
- 使用flex布局实现更稳定的排版效果

### 4. 图片资源管理
```
src/static/
├── images/          # 页面图片
├── icons/           # 图标文件
├── tabs/           # 底部导航图标
│   ├── tower.png
│   ├── tower-active.png
│   ├── tree.png
│   ├── tree-active.png
│   ├── my.png
│   └── my-active.png
└── logo.svg        # 应用Logo
```

### 5. 代码质量优化原则

#### 组件拆分策略
- **单一职责**: 每个组件只负责一个功能模块，职责清晰
- **优先子组件**: 先创建子组件，再实现主组件，降低复杂度
- **合理粒度**: 避免单个组件过于复杂，也避免过度拆分
- **样式完整**: 拆分时确保每个组件的样式完整性

#### 代码结构优化
```vue
<!-- ✅ 优秀的组件结构示例 -->
<template>
  <!-- 语义化的HTML结构 -->
  <view class="page-container">
    <view class="content-section">
      <view class="header-area">
        <text class="title">{{ title }}</text>
      </view>
      <view class="main-content">
        <!-- 内容区域 -->
      </view>
    </view>
  </view>
</template>

<script setup>
// 清晰的逻辑组织
import { ref, reactive } from 'vue'

// 响应式数据
const title = ref('页面标题')
const formData = reactive({
  // 表单数据
})

// 方法定义
const handleSubmit = () => {
  // 处理逻辑
}
</script>

<style lang="scss" scoped>
// 有序的样式组织
.page-container {
  // 页面容器样式

  .content-section {
    // 内容区域样式

    .header-area {
      // 头部样式

      .title {
        // 标题样式
      }
    }
  }
}
</style>
```

#### 命名规范
- **语义化命名**: 类名和变量名要能清晰表达其用途
- **统一风格**: 使用kebab-case命名CSS类，camelCase命名JS变量
- **避免缩写**: 优先使用完整单词，提高可读性
- **层级清晰**: CSS嵌套层级要合理，不超过3-4层

## 📋 样式保持 + 代码质量检查清单

### HTML结构转换质量
- [ ] 所有div标签已转换为view
- [ ] 所有span标签已转换为text
- [ ] 所有img标签已转换为image并添加mode属性
- [ ] 图片路径已更新为/static/开头
- [ ] 已删除工具生成的顶部导航栏代码
- [ ] 页面标题通过pages.json配置
- [ ] 保持原有CSS类名和层级结构
- [ ] HTML结构语义化，层级清晰
- [ ] 类名命名规范，语义明确

### CSS样式精确转换与优化
- [ ] 所有px单位已1:1转换为rpx
- [ ] 颜色值与设计稿完全一致
- [ ] 字体大小精确匹配设计稿
- [ ] 间距和边距数值准确
- [ ] 移除了自定义字体设置
- [ ] 移除了不必要的绝对定位
- [ ] 使用了Sass变量管理颜色和尺寸
- [ ] 保持了原有的布局结构
- [ ] CSS嵌套层级合理（不超过3-4层）
- [ ] 样式组织有序，便于维护
- [ ] 避免了重复样式代码

### JavaScript逻辑与代码质量
- [ ] 使用Vue 3 Composition API
- [ ] 响应式数据使用ref/reactive
- [ ] 事件处理使用@click等Vue语法
- [ ] 集成了uni-app API
- [ ] 保持原有的交互逻辑
- [ ] 变量命名规范，语义清晰
- [ ] 函数职责单一，逻辑清晰
- [ ] 代码注释适当，便于理解
- [ ] 错误处理完善

### 组件结构优化
- [ ] 组件职责单一，功能明确
- [ ] 组件拆分粒度合理
- [ ] 子组件优先创建，主组件简洁
- [ ] 组件间通信清晰
- [ ] 可复用性良好

### 样式一致性验证
- [ ] 页面布局与设计稿完全一致
- [ ] 所有元素位置准确无偏差
- [ ] 颜色显示与设计稿匹配
- [ ] 字体大小和行高正确
- [ ] 交互效果保持原有体验
- [ ] 多平台显示效果一致
- [ ] 性能表现良好
- [ ] 代码可维护性强

## 🎯 样式保持 + 代码优秀最佳实践

### 转换流程（样式保真 + 代码优化）
1. **深度分析设计稿**: 仔细分析设计稿的布局、尺寸、颜色、字体等所有视觉要素
2. **精确测量数值**: 确保所有尺寸、间距、颜色值的准确性
3. **结构规划**: 在保持样式的前提下，规划合理的组件结构和代码组织
4. **逐步转换验证**: 分模块进行转换，每个模块都要验证样式和代码质量
5. **样式对比检查**: 每完成一个模块就与原设计稿进行详细对比
6. **代码质量审查**: 检查命名规范、结构清晰度、可维护性
7. **多平台测试**: 在H5和小程序平台都要验证样式一致性和功能正常

### 组件拆分策略（平衡功能与样式）
1. **优先子组件**: 先创建子组件，再实现主组件，降低主组件复杂度
2. **保持样式完整**: 拆分组件时确保样式不被破坏，每个组件样式独立完整
3. **功能内聚**: 相关功能和样式放在同一个组件中，提高内聚性
4. **简单页面处理**: 对于简单页面，重点关注命名和结构优化，避免过度拆分
5. **样式继承**: 合理利用样式继承，减少重复代码
6. **接口清晰**: 组件间的props和events要设计清晰，便于维护

### 代码质量提升策略
1. **命名优化**: 使用语义化的类名和变量名，提高代码可读性
2. **结构清晰**: 保持HTML结构层级清晰，CSS嵌套合理
3. **逻辑简洁**: JavaScript逻辑要简洁明了，避免复杂的嵌套
4. **注释适当**: 在关键位置添加注释，说明特殊处理和业务逻辑
5. **错误处理**: 添加必要的错误处理，提高代码健壮性

### 质量保证（双重验证）
1. **像素级对比**: 转换后的页面要与设计稿进行像素级对比
2. **交互保真**: 所有交互效果必须与原设计保持一致
3. **代码审查**: 检查代码结构、命名规范、可维护性
4. **性能优化**: 在保持样式不变的前提下优化性能
5. **可维护性**: 确保代码易于理解和修改
6. **文档记录**: 记录特殊处理和样式调整，便于后续维护

## 🔧 常见问题解决

### 1. 导航栏样式问题
- **问题**: 工具生成了顶部导航栏代码，可能与原生导航栏冲突
- **解决**: 删除所有header、nav-bar相关代码
- **配置**: 在pages.json中配置navigationBarTitleText
- **注意**: 确保删除导航栏后页面布局不受影响

### 2. 布局偏移问题
- **问题**: 转换后元素位置与设计稿不符
- **解决**: 检查绝对定位，优先使用flex布局
- **验证**: 与设计稿进行详细对比，确保位置准确

### 3. 颜色显示差异
- **问题**: 转换后颜色与设计稿不一致
- **解决**: 严格按照设计稿颜色值设置，参考新增地址页面的按钮颜色
- **注意**: 避免使用近似颜色，必须精确匹配

### 4. 字体大小问题
- **问题**: 字体显示大小与设计稿不符
- **解决**: 确保px到rpx的1:1转换，检查line-height设置
- **验证**: 在不同设备上测试字体显示效果

### 5. 代码结构问题
- **问题**: 转换后代码结构混乱，难以维护
- **解决**: 重新组织代码结构，优化命名和层级
- **优化**: 合理拆分组件，提高代码可读性

### 6. 性能问题
- **问题**: 转换后页面性能下降
- **解决**: 优化CSS选择器，减少不必要的样式计算
- **注意**: 在保持样式不变的前提下进行性能优化

## 💡 代码优化示例

### 优化前（工具生成）
```vue
<template>
  <div class="container">
    <div class="item" style="width: 200px; height: 100px; background: #ff0000;">
      <span>内容</span>
    </div>
  </div>
</template>

<style>
.container { padding: 20px; }
.item { margin: 10px; }
</style>
```

### 优化后（MyTree项目）
```vue
<template>
  <view class="page-container">
    <view class="content-item">
      <text class="item-text">内容</text>
    </view>
  </view>
</template>

<script setup>
// 清晰的逻辑组织
</script>

<style lang="scss" scoped>
.page-container {
  padding: 40rpx; // 精确转换

  .content-item {
    width: 400rpx;
    height: 200rpx;
    margin: 20rpx;
    background-color: $primary-color; // 使用变量

    .item-text {
      // 文字样式
    }
  }
}
</style>
```

### 优化要点说明
1. **标签转换**: div→view, span→text
2. **单位转换**: px→rpx (1:1精确转换)
3. **样式组织**: 使用Sass嵌套和变量
4. **结构清晰**: 语义化命名，层级分明
5. **样式保持**: 视觉效果完全一致
