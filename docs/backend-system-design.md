# MyTree 果树认养系统后端设计文档

## 1. 项目概述

MyTree 是一个基于微信小程序的果树认养平台，用户可以通过平台认养果树，追踪果树成长过程，并在收获季节收到实物果实。本文档详细设计了该项目的后端系统架构。

## 2. 技术栈

- **运行环境**: Node.js (推荐 v18+)
- **框架**: Express.js
- **数据库**: MySQL
- **ORM**: Drizzle ORM
- **认证**: JWT (JSON Web Token)
- **文件存储**: 服务器本地存储 + Nginx 静态文件服务
- **支付**: 微信支付
- **缓存**: Redis
- **日志**: Winston
- **API文档**: Swagger

## 3. 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序      │────│   API Gateway   │────│   Express App   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │      Redis      │─────────────┤
                       │     (缓存)       │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │     MySQL       │─────────────┤
                       │   (主数据库)      │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │     Nginx       │─────────────┤
                       │   (静态文件)      │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   本地文件系统    │─────────────┘
                       │   (uploads/)    │
                       └─────────────────┘
```

## 4. 数据库设计

### 4.1 用户表 (users)
```typescript
// schema/users.ts
import { mysqlTable, int, varchar, text, timestamp } from 'drizzle-orm/mysql-core';

export const users = mysqlTable('users', {
  id: int('id').primaryKey().autoincrement(),
  openid: varchar('openid', { length: 100 }).unique().notNull(),
  unionid: varchar('unionid', { length: 100 }),
  nickname: varchar('nickname', { length: 50 }),
  avatar: text('avatar'),
  mobile: varchar('mobile', { length: 20 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow()
});
```

### 4.2 果树表 (fruit_trees)
```typescript
// schema/fruitTrees.ts
import { mysqlTable, int, varchar, text, decimal, mysqlEnum, timestamp } from 'drizzle-orm/mysql-core';

export const fruitTrees = mysqlTable('fruit_trees', {
  id: int('id').primaryKey().autoincrement(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  image: text('image'),
  price: decimal('price', { precision: 10, scale: 2 }).notNull(),
  totalStock: int('total_stock').notNull().default(0),
  soldStock: int('sold_stock').notNull().default(0),
  brand: varchar('brand', { length: 50 }),
  productType: varchar('product_type', { length: 50 }),
  diameter: varchar('diameter', { length: 20 }),
  shelfLife: varchar('shelf_life', { length: 50 }),
  origin: varchar('origin', { length: 100 }),
  specPackage: varchar('spec_package', { length: 100 }),
  storageCondition: varchar('storage_condition', { length: 100 }),
  variety: varchar('variety', { length: 50 }),
  deliveryTime: varchar('delivery_time', { length: 100 }),
  adoptTime: varchar('adopt_time', { length: 100 }),
  status: mysqlEnum('status', ['active', 'inactive']).default('active'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow()
});
```

### 4.3 订单表 (orders)
```typescript
// schema/orders.ts
import { mysqlTable, int, varchar, decimal, mysqlEnum, timestamp } from 'drizzle-orm/mysql-core';
import { users } from './users';
import { fruitTrees } from './fruitTrees';
import { userAddresses } from './userAddresses';

export const orders = mysqlTable('orders', {
  id: int('id').primaryKey().autoincrement(),
  userId: int('user_id').notNull().references(() => users.id),
  fruitTreeId: int('fruit_tree_id').notNull().references(() => fruitTrees.id),
  orderNo: varchar('order_no', { length: 50 }).unique().notNull(),
  status: mysqlEnum('status', ['pending', 'paid', 'shipped', 'completed', 'cancelled']).default('pending'),
  adoptionStatus: mysqlEnum('adoption_status', ['adopting', 'ended']).default('adopting'),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),
  addressId: int('address_id').notNull().references(() => userAddresses.id),
  payTime: timestamp('pay_time'),
  shipTime: timestamp('ship_time'),
  completeTime: timestamp('complete_time'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow()
});
```

### 4.4 用户地址表 (user_addresses)
```typescript
// schema/userAddresses.ts
import { mysqlTable, int, varchar, boolean, timestamp } from 'drizzle-orm/mysql-core';
import { users } from './users';

export const userAddresses = mysqlTable('user_addresses', {
  id: int('id').primaryKey().autoincrement(),
  userId: int('user_id').notNull().references(() => users.id),
  consigneeName: varchar('consignee_name', { length: 50 }).notNull(),
  consigneeMobile: varchar('consignee_mobile', { length: 20 }).notNull(),
  region: varchar('region', { length: 200 }).notNull(),
  detailAddress: varchar('detail_address', { length: 500 }).notNull(),
  isDefault: boolean('is_default').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow()
});
```

### 4.5 果树成长日志表 (tree_growth_logs)
```typescript
// schema/treeGrowthLogs.ts
import { mysqlTable, int, varchar, text, json, date, timestamp } from 'drizzle-orm/mysql-core';
import { orders } from './orders';

export const treeGrowthLogs = mysqlTable('tree_growth_logs', {
  id: int('id').primaryKey().autoincrement(),
  orderId: int('order_id').notNull().references(() => orders.id),
  title: varchar('title', { length: 100 }).notNull(),
  content: text('content'),
  images: json('images').$type<string[]>(),
  logDate: date('log_date').notNull(),
  createdAt: timestamp('created_at').defaultNow()
});
```

## 5. API 设计

### 5.1 认证相关
```
POST /api/auth/wx-login        # 微信登录
POST /api/auth/bind-mobile     # 绑定手机号
```

### 5.2 用户相关
```
GET    /api/users/profile      # 获取用户信息
PUT    /api/users/profile      # 更新用户信息
GET    /api/users/:id          # 根据ID获取用户信息
```

### 5.3 果树相关
```
GET    /api/fruit-trees        # 获取果树列表
GET    /api/fruit-trees/:id    # 获取果树详情
POST   /api/fruit-trees        # 创建果树（管理员）
PUT    /api/fruit-trees/:id    # 更新果树（管理员）
DELETE /api/fruit-trees/:id    # 删除果树（管理员）
```

### 5.4 订单相关
```
GET    /api/orders             # 获取订单列表
POST   /api/orders             # 创建订单
GET    /api/orders/:id         # 获取订单详情
PUT    /api/orders/:id         # 更新订单状态
DELETE /api/orders/:id         # 删除订单
POST   /api/orders/:id/pay     # 支付订单
```

### 5.5 地址相关
```
GET    /api/addresses          # 获取地址列表
POST   /api/addresses          # 创建地址
GET    /api/addresses/:id      # 获取地址详情
PUT    /api/addresses/:id      # 更新地址
DELETE /api/addresses/:id      # 删除地址
```

### 5.6 文件上传相关
```
POST   /api/upload/single        # 单文件上传
POST   /api/upload/multiple      # 多文件上传
GET    /api/files/:path(*)       # 获取文件
DELETE /api/files/:path(*)       # 删除文件（管理员）
```
### 5.7 果树日志相关
```
GET    /api/orders/:id/logs    # 获取果树成长日志
POST   /api/orders/:id/logs    # 添加成长日志（管理员）
PUT    /api/logs/:id           # 更新日志（管理员）
DELETE /api/logs/:id           # 删除日志（管理员）
```

## 6. 项目结构

```
backend/
├── src/
│   ├── config/              # 配置文件
│   │   ├── database.js      # 数据库配置
│   │   ├── redis.js         # Redis配置
│   │   ├── wechat.js        # 微信配置
│   │   └── index.js         # 主配置
│   ├── controllers/         # 控制器
│   │   ├── auth.js          # 认证控制器
│   │   ├── users.js         # 用户控制器
│   │   ├── fruitTrees.js    # 果树控制器
│   │   ├── orders.js        # 订单控制器
│   │   ├── addresses.js     # 地址控制器
│   │   └── treeLogs.js      # 果树日志控制器
│   ├── db/                  # 数据库相关
│   │   ├── schema/          # Drizzle 模式定义
│   │   │   ├── users.ts     # 用户模式
│   │   │   ├── fruitTrees.ts# 果树模式
│   │   │   ├── orders.ts    # 订单模式
│   │   │   ├── userAddresses.ts # 地址模式
│   │   │   ├── treeGrowthLogs.ts # 成长日志模式
│   │   │   └── index.ts     # 模式入口
│   │   ├── migrations/      # 数据库迁移
│   │   ├── connection.ts    # 数据库连接
│   │   └── index.ts         # 数据库入口
│   ├── routes/              # 路由
│   │   ├── auth.js          # 认证路由
│   │   ├── users.js         # 用户路由
│   │   ├── fruitTrees.js    # 果树路由
│   │   ├── orders.js        # 订单路由
│   │   ├── addresses.js     # 地址路由
│   │   ├── treeLogs.js      # 日志路由
│   │   └── index.js         # 路由入口
│   ├── middleware/          # 中间件
│   │   ├── auth.js          # 认证中间件
│   │   ├── validation.js    # 验证中间件
│   │   ├── rateLimit.js     # 限流中间件
│   │   └── errorHandler.js  # 错误处理中间件
│   ├── services/            # 业务服务
│   │   ├── wechatService.js # 微信服务
│   │   ├── paymentService.js# 支付服务
│   │   ├── fileService.js   # 文件上传服务
│   │   └── notificationService.js # 通知服务
│   ├── utils/               # 工具函数
│   │   ├── logger.js        # 日志工具
│   │   ├── validator.js     # 验证工具
│   │   ├── helper.js        # 辅助函数
│   │   └── constants.js     # 常量定义
│   └── app.js               # 应用入口
├── drizzle/                 # Drizzle 配置和迁移
│   ├── migrations/          # 生成的迁移文件
│   └── meta/                # 迁移元数据
├── drizzle.config.ts        # Drizzle 配置文件
├── seeds/                   # 种子数据
├── uploads/                 # 上传文件目录
│   ├── images/              # 图片文件
│   ├── avatars/             # 用户头像
│   └── temp/                # 临时文件
├── public/                  # 静态资源
│   └── uploads/             # 公开访问的上传文件
├── tests/                   # 测试文件
├── docs/                    # 文档
├── package.json
├── .env.example
└── README.md
```

## 7. 中间件设计

### 7.1 认证中间件
```typescript
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { db } from '@/db';
import { users } from '@/db/schema/users';
import { eq } from 'drizzle-orm';

interface AuthRequest extends Request {
  user?: typeof users.$inferSelect;
}

const authMiddleware = async (req: AuthRequest, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization;
    if (!token) {
      return res.status(401).json({ code: 401, msg: '未提供认证Token' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { userId: number };
    const [user] = await db.select().from(users).where(eq(users.id, decoded.userId));
    
    if (!user) {
      return res.status(401).json({ code: 401, msg: '用户不存在' });
    }
    
    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ code: 401, msg: 'Token无效' });
  }
};
```

### 7.2 错误处理中间件
```typescript
import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

const errorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  logger.error(err.message, err);
  
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      code: 400,
      msg: '数据验证失败',
      errors: err.errors
    });
  }
  
  res.status(500).json({
    code: 500,
    msg: '服务器内部错误'
  });
};
```

## 8. 核心业务逻辑

### 8.1 微信登录流程
1. 小程序调用 `wx.login()` 获取 code
2. 前端将 code 发送到后端 `/api/auth/wx-login`
3. 后端使用 code 向微信服务器换取 openid 和 session_key
4. 查询数据库中是否存在该 openid 用户
5. 如果不存在则创建新用户，存在则更新登录时间
6. 生成 JWT Token 返回给前端

### 8.2 订单创建流程
```typescript
// controllers/orders.ts
import { db } from '@/db';
import { orders, fruitTrees } from '@/db/schema';
import { eq } from 'drizzle-orm';

export const createOrder = async (req: AuthRequest, res: Response) => {
  const { fruitTreeId, quantity, addressId } = req.body;
  
  try {
    await db.transaction(async (tx) => {
      // 1. 验证库存
      const [tree] = await tx.select().from(fruitTrees).where(eq(fruitTrees.id, fruitTreeId));
      if (!tree || tree.totalStock - tree.soldStock < quantity) {
        throw new Error('库存不足');
      }
      
      // 2. 创建订单
      const orderData = {
        userId: req.user!.id,
        fruitTreeId,
        orderNo: generateOrderNo(),
        totalAmount: tree.price * quantity,
        addressId,
        status: 'pending' as const
      };
      
      const [newOrder] = await tx.insert(orders).values(orderData);
      
      // 3. 更新库存
      await tx.update(fruitTrees)
        .set({ soldStock: tree.soldStock + quantity })
        .where(eq(fruitTrees.id, fruitTreeId));
      
      return newOrder;
    });
    
    res.json({ code: 200, msg: 'success', data: newOrder });
  } catch (error) {
    res.status(400).json({ code: 400, msg: error.message });
  }
};

### 8.3 支付流程
1. 前端调用支付接口
2. 后端调用微信支付统一下单接口
3. 返回支付参数给前端
4. 前端调用微信支付
5. 接收微信支付回调
6. 更新订单状态为已支付

## 9. 安全考虑

### 9.1 API 安全
- 使用 HTTPS 加密传输
- JWT Token 过期时间设置为合理范围（建议7天）
- 实施 API 频率限制
- 输入数据验证和过滤
- SQL 注入防护（使用 ORM）

### 9.2 数据安全
- 敏感信息加密存储
- 数据库访问权限控制
- 定期备份数据库
- 日志记录但不包含敏感信息
- 文件上传安全检查（文件类型、大小限制）
- 路径遍历攻击防护

## 10. 性能优化

### 10.1 数据库优化
- 合理设计索引
- 使用 Drizzle 的查询优化功能
- 数据库连接池管理
- 读写分离（如需要）
- 利用 Drizzle 的类型安全查询避免错误

### 10.2 缓存策略
- Redis 缓存热点数据
- 果树列表缓存
- 用户信息缓存
- API 响应缓存

### 10.3 文件和静态资源优化
- 图片自动压缩和格式转换（Sharp）
- Nginx 静态文件缓存
- 文件访问权限控制
- 定时清理临时文件

## 11. 监控和日志

### 11.1 日志管理
- 使用 Winston 进行日志管理
- 按级别记录日志（error, warn, info, debug）
- 日志轮转和清理
- 结构化日志格式

### 11.2 监控指标
- API 响应时间
- 错误率统计
- 数据库性能监控
- 服务器资源使用情况

## 12. 部署方案

### 12.1 环境配置
- **开发环境**: 本地开发
- **测试环境**: 功能测试
- **生产环境**: 正式运行

### 12.2 部署流程
1. 代码构建和打包
2. 数据库迁移
3. 服务器部署
4. 负载均衡配置
5. 监控和日志配置

### 12.3 容器化部署（可选）
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 13. API 响应格式规范

### 13.1 成功响应
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    // 响应数据
  }
}
```

### 13.2 错误响应
```json
{
  "code": 400,
  "msg": "错误描述",
  "errors": [
    // 详细错误信息（可选）
  ]
}
```

### 13.3 分页响应
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "total": 100,
    "page": 1,
    "size": 10,
    "list": [
      // 数据列表
    ]
  }
}
```

## 14. 版本控制和更新策略

### 14.1 API 版本控制
- URL 版本控制: `/api/v1/`
- 向后兼容性考虑
- 版本弃用通知机制

### 14.2 数据库迁移
- 使用 Drizzle Kit 迁移管理
- 版本化迁移文件
- 自动生成迁移文件
- 回滚机制

## 15. 测试策略

### 15.1 单元测试
- 使用 Jest 进行单元测试
- 覆盖核心业务逻辑
- Mock 外部依赖

### 15.2 集成测试
- API 端点测试
- 数据库集成测试
- 第三方服务集成测试

### 15.3 压力测试
- 使用 Artillery 或 JMeter
- 测试 API 性能上限
- 数据库性能测试

## 16. Drizzle ORM 配置和使用

### 16.1 Drizzle 配置文件
```typescript
// drizzle.config.ts
import type { Config } from 'drizzle-kit';

export default {
  schema: './src/db/schema/*',
  out: './drizzle',
  driver: 'mysql2',
  dbCredentials: {
    host: process.env.DB_HOST!,
    port: Number(process.env.DB_PORT!),
    user: process.env.DB_USER!,
    password: process.env.DB_PASSWORD!,
    database: process.env.DB_NAME!,
  },
} satisfies Config;
```

### 16.2 数据库连接配置
```typescript
// src/db/connection.ts
import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import * as schema from './schema';

const connection = mysql.createPool({
  host: process.env.DB_HOST!,
  port: Number(process.env.DB_PORT!),
  user: process.env.DB_USER!,
  password: process.env.DB_PASSWORD!,
  database: process.env.DB_NAME!,
  connectionLimit: 10,
});

export const db = drizzle(connection, { schema, mode: 'default' });
```

### 16.3 Schema 导出文件
```typescript
// src/db/schema/index.ts
export * from './users';
export * from './fruitTrees';
export * from './orders';
export * from './userAddresses';
export * from './treeGrowthLogs';

// 关系定义
import { relations } from 'drizzle-orm';
import { users, orders, fruitTrees, userAddresses, treeGrowthLogs } from './';

export const usersRelations = relations(users, ({ many }) => ({
  orders: many(orders),
  addresses: many(userAddresses),
}));

export const ordersRelations = relations(orders, ({ one, many }) => ({
  user: one(users, {
    fields: [orders.userId],
    references: [users.id],
  }),
  fruitTree: one(fruitTrees, {
    fields: [orders.fruitTreeId],
    references: [fruitTrees.id],
  }),
  address: one(userAddresses, {
    fields: [orders.addressId],
    references: [userAddresses.id],
  }),
  growthLogs: many(treeGrowthLogs),
}));

export const fruitTreesRelations = relations(fruitTrees, ({ many }) => ({
  orders: many(orders),
}));

export const userAddressesRelations = relations(userAddresses, ({ one, many }) => ({
  user: one(users, {
    fields: [userAddresses.userId],
    references: [users.id],
  }),
  orders: many(orders),
}));

export const treeGrowthLogsRelations = relations(treeGrowthLogs, ({ one }) => ({
  order: one(orders, {
    fields: [treeGrowthLogs.orderId],
    references: [orders.id],
  }),
}));
```

### 16.4 查询示例
```typescript
// 获取用户及其订单信息
export const getUserWithOrders = async (userId: number) => {
  return await db.query.users.findFirst({
    where: eq(users.id, userId),
    with: {
      orders: {
        with: {
          fruitTree: true,
          address: true,
          growthLogs: true,
        },
      },
      addresses: true,
    },
  });
};

// 获取果树列表（带分页）
export const getFruitTreeList = async (page: number = 1, size: number = 10) => {
  const offset = (page - 1) * size;
  
  const trees = await db.select().from(fruitTrees)
    .where(eq(fruitTrees.status, 'active'))
    .limit(size)
    .offset(offset);
    
  const total = await db.select({ count: count() }).from(fruitTrees)
    .where(eq(fruitTrees.status, 'active'));
    
  return {
    list: trees,
    total: total[0].count,
    page,
    size,
  };
};

// 复杂查询：获取用户的有效订单统计
export const getUserOrderStats = async (userId: number) => {
  return await db.select({
    totalOrders: count(),
    totalAmount: sum(orders.totalAmount),
    adoptingCount: count(case(when(eq(orders.adoptionStatus, 'adopting'), 1))),
  })
  .from(orders)
  .where(and(
    eq(orders.userId, userId),
    ne(orders.status, 'cancelled')
  ));
};
```

### 16.5 Drizzle Kit 命令
```json
// package.json scripts
{
  "scripts": {
    "db:generate": "drizzle-kit generate:mysql",
    "db:migrate": "drizzle-kit push:mysql",
    "db:studio": "drizzle-kit studio",
    "db:seed": "tsx src/db/seed.ts"
  }
}
```

### 16.6 类型推断
```typescript
// 自动推断的类型
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Order = typeof orders.$inferSelect;
export type NewOrder = typeof orders.$inferInsert;
export type FruitTree = typeof fruitTrees.$inferSelect;
export type NewFruitTree = typeof fruitTrees.$inferInsert;

// 在控制器中使用
export const createUser = async (userData: NewUser) => {
  const [newUser] = await db.insert(users).values(userData);
  return newUser;
};
```

## 17. 文件上传和存储

### 17.1 文件上传服务
```typescript
// services/fileService.ts
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import sharp from 'sharp';

// 配置文件存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = 'uploads/';
    
    if (file.fieldname === 'avatar') {
      uploadPath += 'avatars/';
    } else if (file.mimetype.startsWith('image/')) {
      uploadPath += 'images/';
    } else {
      uploadPath += 'temp/';
    }
    
    // 确保目录存在
    fs.mkdir(uploadPath, { recursive: true });
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req: any, file: any, cb: any) => {
  // 允许的文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
    files: 10 // 最多10个文件
  }
});

// 图片处理服务
export const processImage = async (filePath: string, options: {
  width?: number;
  height?: number;
  quality?: number;
}) => {
  const { width = 800, height = 600, quality = 80 } = options;
  
  const outputPath = filePath.replace(/\.[^/.]+$/, '_processed.webp');
  
  await sharp(filePath)
    .resize(width, height, { fit: 'inside', withoutEnlargement: true })
    .webp({ quality })
    .toFile(outputPath);
    
  // 删除原文件
  await fs.unlink(filePath);
  
  return outputPath;
};

// 文件清理服务
export const cleanupTempFiles = async () => {
  const tempDir = 'uploads/temp/';
  const files = await fs.readdir(tempDir);
  const now = Date.now();
  const oneDay = 24 * 60 * 60 * 1000;
  
  for (const file of files) {
    const filePath = path.join(tempDir, file);
    const stats = await fs.stat(filePath);
    
    if (now - stats.mtime.getTime() > oneDay) {
      await fs.unlink(filePath);
    }
  }
};
```

### 17.2 文件上传 API
```typescript
// routes/upload.ts
import { Router } from 'express';
import { upload, processImage } from '@/services/fileService';
import { authMiddleware } from '@/middleware/auth';

const router = Router();

// 单文件上传
router.post('/single', authMiddleware, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ code: 400, msg: '未选择文件' });
    }
    
    let filePath = req.file.path;
    
    // 如果是图片，进行压缩处理
    if (req.file.mimetype.startsWith('image/')) {
      filePath = await processImage(filePath, {
        width: 1200,
        height: 800,
        quality: 85
      });
    }
    
    // 返回文件访问URL
    const fileUrl = `/uploads/${path.relative('uploads/', filePath)}`;
    
    res.json({
      code: 200,
      msg: 'success',
      data: {
        filename: req.file.filename,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        url: fileUrl
      }
    });
  } catch (error) {
    res.status(500).json({ code: 500, msg: '文件上传失败' });
  }
});

// 多文件上传
router.post('/multiple', authMiddleware, upload.array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ code: 400, msg: '未选择文件' });
    }
    
    const files = req.files as Express.Multer.File[];
    const results = [];
    
    for (const file of files) {
      let filePath = file.path;
      
      if (file.mimetype.startsWith('image/')) {
        filePath = await processImage(filePath, {
          width: 1200,
          height: 800,
          quality: 85
        });
      }
      
      const fileUrl = `/uploads/${path.relative('uploads/', filePath)}`;
      
      results.push({
        filename: file.filename,
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: fileUrl
      });
    }
    
    res.json({ code: 200, msg: 'success', data: results });
  } catch (error) {
    res.status(500).json({ code: 500, msg: '文件上传失败' });
  }
});

export default router;
```

### 17.3 Nginx 静态文件配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name your-domain.com;
    
    # API 路由
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件服务
    location /uploads/ {
        alias /path/to/your/backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin *;
        
        # 图片格式优化
        location ~* \.(jpg|jpeg|png|gif|webp)$ {
            add_header Vary Accept;
            expires 1y;
        }
        
        # 安全设置
        location ~ \.(php|php5|sh|pl|py)$ {
            deny all;
        }
    }
    
    # 文件上传大小限制
    client_max_body_size 10M;
}
```

### 17.4 文件管理中间件
```typescript
// middleware/fileManagement.ts
import { Request, Response, NextFunction } from 'express';
import path from 'path';
import fs from 'fs/promises';

// 文件访问权限检查
export const checkFileAccess = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const filePath = req.params.path;
    const fullPath = path.join('uploads', filePath);
    
    // 检查文件是否存在
    await fs.access(fullPath);
    
    // 检查文件是否在允许的目录内
    const resolvedPath = path.resolve(fullPath);
    const uploadsPath = path.resolve('uploads');
    
    if (!resolvedPath.startsWith(uploadsPath)) {
      return res.status(403).json({ code: 403, msg: '文件访问被拒绝' });
    }
    
    next();
  } catch (error) {
    res.status(404).json({ code: 404, msg: '文件不存在' });
  }
};

// 文件删除
export const deleteFile = async (filePath: string) => {
  try {
    const fullPath = path.join('uploads', filePath);
    await fs.unlink(fullPath);
    return true;
  } catch (error) {
    console.error('删除文件失败:', error);
    return false;
  }
};
```

### 17.5 定时清理任务
```typescript
// tasks/fileCleanup.ts
import cron from 'node-cron';
import { cleanupTempFiles } from '@/services/fileService';
import { logger } from '@/utils/logger';

// 每天凌晨2点清理临时文件
cron.schedule('0 2 * * *', async () => {
  try {
    logger.info('开始清理临时文件...');
    await cleanupTempFiles();
    logger.info('临时文件清理完成');
  } catch (error) {
    logger.error('临时文件清理失败:', error);
  }
});
```

### 17.6 文件访问API
```typescript
// routes/files.ts
import { Router } from 'express';
import { checkFileAccess } from '@/middleware/fileManagement';
import path from 'path';

const router = Router();

// 获取文件
router.get('/:path(*)', checkFileAccess, (req, res) => {
  const filePath = req.params.path;
  const fullPath = path.join('uploads', filePath);
  
  res.sendFile(path.resolve(fullPath));
});

export default router;
```

---

*本文档将随着项目发展持续更新和完善。*