<template>
  <view class="product-info">
    <!-- 产品名称 -->
    <text class="product-name">{{ name }}</text>
    
    <!-- 产品描述 -->
    <text class="product-description">{{ description }}</text>
    
    <!-- 价格和销量 -->
    <view class="price-section">
      <text class="price">¥{{ price.toFixed(2) }}</text>
      <text class="sold-count">已购买：{{ salesCount }}棵</text>
    </view>
  </view>
</template>

<script setup>
// Props
const props = defineProps({
  name: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  price: {
    type: Number,
    default: 0
  },
  salesCount: {
    type: Number,
    default: 0
  }
})
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-gray: #999999;
$text-light-gray: #767676;

.product-info {
  background-color: $white-color;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.product-name {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  line-height: 55rpx;
  color: $text-dark;
  margin-bottom: 28rpx;
}

.product-description {
  display: block;
  font-size: 30rpx;
  line-height: 35rpx;
  color: $text-gray;
  margin-bottom: 28rpx;
}

.price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .price {
    font-size: 40rpx;
    font-weight: 700;
    line-height: 55rpx;
    color: $primary-color;
  }
  
  .sold-count {
    font-size: 28rpx;
    line-height: 33rpx;
    color: $text-light-gray;
  }
}
</style>
