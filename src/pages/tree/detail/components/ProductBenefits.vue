<template>
  <view class="benefits-container">
    <view class="benefits-text">
      {{ benefits }}
    </view>
  </view>
</template>

<script setup>
// Props
const props = defineProps({
  benefits: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
// 设计变量
$white-color: #ffffff;
$text-dark: #1a1a1a;

.benefits-container {
  background-color: $white-color;
  margin-bottom: 20rpx;
}

.benefits-text {
  padding: 28rpx 40rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  color: $text-dark;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
