<template>
  <view class="swiper-container">
    <swiper 
      class="swiper" 
      :indicator-dots="false"
      :autoplay="true"
      :interval="3000"
      :duration="500"
      @change="onSwiperChange"
    >
      <swiper-item v-for="(image, index) in images" :key="index">
        <image 
          :src="image" 
          mode="aspectFill" 
          class="swiper-image"
        />
      </swiper-item>
    </swiper>
    
    <!-- 页码指示器 -->
    <view class="page-indicator">
      <text class="page-text">{{ currentIndex + 1 }}/{{ images.length }}</text>
    </view>
    
    <!-- 圆点指示器 -->
    <view class="dots-indicator">
      <view 
        v-for="(image, index) in images" 
        :key="index"
        class="dot"
        :class="{ 'active': index === currentIndex }"
      ></view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// Props
const props = defineProps({
  images: {
    type: Array,
    default: () => []
  }
})

// 响应式数据
const currentIndex = ref(0)

// 轮播图切换事件
const onSwiperChange = (e) => {
  currentIndex.value = e.detail.current
}
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-white: #ffffff;
$bg-dark: rgba(0, 0, 0, 0.8);
$dot-gray: #d8d8d8;

.swiper-container {
  position: relative;
  width: 100%;
  height: 750rpx;
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

// 页码指示器
.page-indicator {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  background-color: $bg-dark;
  border-radius: 24rpx;
  padding: 8rpx 16rpx;
  z-index: 10;
  
  .page-text {
    color: $text-white;
    font-size: 24rpx;
    line-height: 32rpx;
  }
}

// 圆点指示器
.dots-indicator {
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16rpx;
  z-index: 10;
  
  .dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: $dot-gray;
    transition: background-color 0.3s ease;
    
    &.active {
      background-color: $primary-color;
    }
  }
}
</style>
