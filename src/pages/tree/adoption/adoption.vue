<script setup>
import { ref, onMounted } from 'vue'
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import RenewalModal from './components/RenewalModal.vue'
import CertificateModal from './components/CertificateModal.vue'
import Tab from '@/components/Tab.vue'
import EmptyState from '@/components/EmptyState.vue'
import AdoptionCard from '@/components/AdoptionCard.vue'
import { getAdoptionList } from '@/api/order'

const activeTab = ref(0) // 0 for '认养中', 1 for '已结束'
const isRenewalModalVisible = ref(false)
const isCertificateModalVisible = ref(false)
const selectedAdoption = ref(null) // 存储当前选中的认养数据
const renewalAdoption = ref(null) // 存储需要续费的认养数据



const showRenewalModal = (adoption = null) => {
  renewalAdoption.value = adoption
  isRenewalModalVisible.value = true
}

const hideRenewalModal = () => {
  isRenewalModalVisible.value = false
  renewalAdoption.value = null
}

// 检查是否即将到期（30天内）
const checkExpirationSoon = (adoption) => {
  if (!adoption?.endDate) return false

  const endDate = new Date(adoption.endDate)
  const currentDate = new Date()
  const diffTime = endDate - currentDate
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return diffDays > 0 && diffDays <= 30
}

// 检查并显示到期提醒
const checkAndShowExpirationReminder = () => {
  // 查找即将到期的认养
  const expiringAdoption = adoptionList.value.find(adoption => checkExpirationSoon(adoption))

  if (expiringAdoption) {
    // 延迟显示，避免与页面加载冲突
    setTimeout(() => {
      showRenewalModal(expiringAdoption)
    }, 1000)
  }
}

// 处理续费
const handleRenewal = (adoption) => {
  hideRenewalModal()
  // 跳转到果树列表页面（tab页面）
  uni.switchTab({
    url: '/pages/tree/tree'
  })
}



const showCertificateModal = (adoption) => {
  selectedAdoption.value = adoption
  isCertificateModalVisible.value = true
}

const hideCertificateModal = () => {
  isCertificateModalVisible.value = false
  selectedAdoption.value = null
}

// 响应式数据
const adoptionList = ref([])
const loading = ref(false)
const loadMoreStatus = ref('more') // more, loading, noMore
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

// 加载更多文本配置
const loadMoreText = ref({
  contentdown: '上拉显示更多',
  contentrefresh: '正在加载...',
  contentnomore: '没有更多数据了'
})

// 加载数据
const loadData = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      pageNum.value = 1
      adoptionList.value = []
      hasMore.value = true
    }

    loading.value = true
    loadMoreStatus.value = 'loading'

    // 状态映射：认养中=1（正常），已结束=2（过期）+3（禁止）
    // activeTab: 0-认养中, 1-已结束
    const statusMapping = [1, 2] // 认养中使用状态1，已结束暂时使用状态2

    const res = await getAdoptionList({
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      status: statusMapping[activeTab.value]
    })

    const { rows, total } = res

    if (isRefresh) {
      adoptionList.value = rows
    } else {
      adoptionList.value.push(...rows)
    }

    // 检查是否有即将到期的认养（仅在认养中标签页且首次加载时检查）
    if (isRefresh && activeTab.value === 0) {
      checkAndShowExpirationReminder()
    }

    hasMore.value = adoptionList.value.length < total
    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore'
    pageNum.value++
  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
    loadMoreStatus.value = 'more'
  } finally {
    loading.value = false
  }
}

// 下拉刷新处理函数
const handlePullDownRefresh = async () => {
  await loadData(true)
  uni.stopPullDownRefresh()
}

// 上拉加载更多处理函数
const handleReachBottom = () => {
  if (hasMore.value && loadMoreStatus.value !== 'loading') {
    loadData(false)
  }
}

// uni-load-more组件点击事件
const onLoadMore = () => {
  if (hasMore.value && loadMoreStatus.value !== 'loading') {
    loadData(false)
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadData(true)
})

// 注册页面生命周期
onPullDownRefresh(handlePullDownRefresh)
onReachBottom(handleReachBottom)

const switchTab = (tabIndex) => {
  activeTab.value = tabIndex
  loadData(true)
}

const navigateToEquity = (adoption) => {
  // 将认养数据存储到全局状态或通过页面参数传递
  uni.navigateTo({
    url: `/pages/equity/equity?adoptionId=${adoption.id}`
  })
}

const navigateToAdopt = () => {
  uni.switchTab({
    url: '/pages/tree/tree'
  })
}
</script>

<template>
  <view class="page-container">
    <!-- Tabs Section -->
    <view class="tabs-section">
      <Tab text="认养中" :active="activeTab === 0" @click="switchTab(0)" />
      <Tab text="已结束" :active="activeTab === 1" @click="switchTab(1)" />
    </view>

    <!-- 空状态 -->
    <EmptyState
      v-if="!loading && adoptionList.length === 0"
      title="暂时还没有领养果树哦"
      button-text="认养果树"
      @button-click="navigateToAdopt"
    />

    <!-- Adoption Card List -->
    <AdoptionCard
      v-for="adoption in adoptionList"
      :key="adoption.id"
      :adoption="adoption"
      @navigate-to-equity="(adoptionData) => navigateToEquity(adoptionData)"
      @show-certificate="(adoptionData) => showCertificateModal(adoptionData)"
    />

    <!-- 加载更多组件 -->
    <uni-load-more
      v-if="adoptionList.length > 0"
      :status="loadMoreStatus"
      @clickLoadMore="onLoadMore"
      :content-text="loadMoreText"
    />



    <RenewalModal
      v-if="isRenewalModalVisible"
      :adoption="renewalAdoption"
      @close="hideRenewalModal"
      @renew="handleRenewal"
    />
    <CertificateModal
      v-if="isCertificateModalVisible"
      :adoption="selectedAdoption"
      @close="hideCertificateModal"
    />
  </view>
</template>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 40rpx;
}

.tabs-section {
  width: 100%;
  height: 96rpx;
  background-color: #ffffff;
  border-bottom: 1.5rpx solid #eeeeee;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 100rpx;
}



</style>
