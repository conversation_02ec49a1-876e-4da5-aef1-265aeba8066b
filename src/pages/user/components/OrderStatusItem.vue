<template>
  <view class="order-status-item" @click="handleClick">
    <view class="icon-container">
      <image 
        :src="icon" 
        class="status-icon"
        mode="aspectFit"
      />
    </view>
    <text class="status-title">{{ title }}</text>
  </view>
</template>

<script setup>
const emit = defineEmits(['click'])

defineProps({
  icon: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  }
})

const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
// 颜色变量
$text-color: #3d3d3d;

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 22rpx;
  cursor: pointer;
  
  .icon-container {
    width: 62rpx;
    height: 53rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .status-icon {
      max-width: 100%;
      max-height: 100%;
    }
  }
  
  .status-title {
    font-size: 24rpx;
    color: $text-color;
    line-height: 28rpx;
    text-align: center;
  }
}
</style>
