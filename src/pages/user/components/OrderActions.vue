<template>
  <view class="order-grid">
    <OrderStatusItem
      v-for="(item, index) in orderStatus"
      :key="index"
      :icon="item.icon"
      :title="item.title"
      @click="$emit('orderClick', index)"
      class="order-item"
    />
  </view>
</template>

<script setup>
import OrderStatusItem from './OrderStatusItem.vue'

defineProps({
  orderStatus: {
    type: Array,
    default: () => []
  }
})

defineEmits(['orderClick'])
</script>

<style lang="scss" scoped>
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.order-grid {
  display: flex;
  justify-content: space-between;

  .order-item {
    flex: 1;
    @include flex-center;
    flex-direction: column;
  }
}
</style>