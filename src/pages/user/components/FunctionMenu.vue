<template>
  <view class="function-menu">
    <view class="menu-list">
      <!-- 收货地址 -->
      <view class="menu-item">
        <view class="item-content" @click="handleAddressClick">
          <view class="icon-container">
            <image
              src="/static/address.svg"
              class="menu-icon"
              mode="aspectFit"
            />
          </view>
          <text class="menu-title">收货地址</text>
          <view class="arrow-container">
            <image
              src="/static/icons/arrow-right.svg"
              class="arrow-icon"
              mode="aspectFit"
            />
          </view>
        </view>
        <view class="divider"></view>
      </view>

      <!-- 商务合作 -->
      <view class="menu-item">
        <view class="item-content" @click="handleBusinessClick">
          <view class="icon-container">
            <image
              src="/static/business-cooperation.svg"
              class="menu-icon"
              mode="aspectFit"
            />
          </view>
          <text class="menu-title">商务合作</text>
          <view class="arrow-container">
            <image
              src="/static/icons/arrow-right.svg"
              class="arrow-icon"
              mode="aspectFit"
            />
          </view>
        </view>
        <view class="divider"></view>
      </view>

      <!-- 联系客服 -->
      <view class="menu-item">
        <button class="contact-service-menu" open-type="contact">
          <view class="item-content">
            <view class="icon-container">
              <image
                src="/static/contact-service-2.svg"
                class="menu-icon"
                mode="aspectFit"
              />
            </view>
            <text class="menu-title">联系客服</text>
            <view class="arrow-container">
              <image
                src="/static/icons/arrow-right.svg"
                class="arrow-icon"
                mode="aspectFit"
              />
            </view>
          </view>
        </button>
        <view class="divider"></view>
      </view>

      <!-- 关于我们 -->
      <view class="menu-item">
        <view class="item-content" @click="handleAboutUsClick">
          <view class="icon-container">
            <image
              src="/static/adoption-benefits.svg"
              class="menu-icon"
              mode="aspectFit"
            />
          </view>
          <text class="menu-title">关于我们</text>
          <view class="arrow-container">
            <image
              src="/static/icons/arrow-right.svg"
              class="arrow-icon"
              mode="aspectFit"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>

// 处理收货地址点击
const handleAddressClick = () => {
  uni.navigateTo({
    url: '/pages/address/list/list'
  })
}

// 处理商务合作点击
const handleBusinessClick = () => {
  uni.navigateTo({
    url: '/pages/company/business-cooperation/business-cooperation'
  })
}

// 处理关于我们点击
const handleAboutUsClick = () => {
  uni.navigateTo({
    url: '/pages/company/about-us/about-us'
  })
}


</script>

<style lang="scss" scoped>
// 颜色变量
$white-color: #ffffff;
$text-dark: #1a1a1a;
$border-gray: #e9e9e9;

@mixin card-style {
  background-color: $white-color;
  border-radius: 20rpx;
}

.function-menu {
  margin: 0 32rpx 36rpx;

  .menu-list {
    @include card-style;
    overflow: hidden;
    margin-bottom: 80rpx;
    padding: 0 32rpx;

    .menu-item {
      position: relative;

      .item-content {
        height: 100rpx;
        display: flex;
        align-items: center;
        gap: 16rpx;

        .icon-container {
          width: 32rpx;
          height: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .menu-icon {
            width: 100%;
            height: 100%;
          }
        }

        .menu-title {
          flex: 1;
          font-size: 30rpx;
          color: $text-dark;
          line-height: 35rpx;
        }

        .arrow-container {
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          .arrow-icon {
            width: 48rpx;
            height: 48rpx;
          }
        }
      }

      .divider {
        height: 1rpx;
        background-color: $border-gray;
        margin: 0 32rpx 0 32rpx; // 和图标对齐
      }

      &:last-child .divider {
        display: none;
      }

      .contact-service-menu {
        width: 100%;
        background: none;
        border: none;
        padding: 0;
        margin: 0;
        text-align: left;
        outline: none;

        &::after {
          border: none;
        }
      }
    }
  }
}
</style>
