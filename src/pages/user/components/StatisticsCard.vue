<template>
  <view class="statistics-card" :style="{ backgroundColor: bgColor }" @click="handleClick">
    <view class="card-header">
      <text class="card-title">{{ title }}</text>
    </view>
    <view class="card-content">
      <view class="count-section">
        <text class="count-number">{{ count }}</text>
        <text class="count-unit">{{ unit }}</text>
      </view>
      <view class="icon-section">
        <image
          :src="icon"
          class="card-icon"
          mode="aspectFit"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(['click'])

defineProps({
  title: {
    type: String,
    required: true
  },
  count: {
    type: Number,
    required: true
  },
  unit: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  bgColor: {
    type: String,
    default: '#ffffff'
  }
})

const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
// 颜色变量
$text-dark: #1a1a1a;
$text-light: #767676;
$count-color: #ea0000;

.statistics-card {
  flex: 1;
  height: 180rpx;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.2s ease;

  &:active {
    opacity: 0.8;
    transform: scale(0.98);
  }
  
  .card-header {
    .card-title {
      font-size: 30rpx;
      font-weight: 500;
      color: $text-dark;
      line-height: 35rpx;
    }
  }
  
  .card-content {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    margin-top: 4rpx;

    .count-section {
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .count-number {
        font-size: 48rpx;
        font-weight: 700;
        color: $count-color;
        line-height: 56rpx;
      }

      .count-unit {
        font-size: 24rpx;
        color: $text-light;
        line-height: 28rpx;
      }
    }

    .icon-section {
      display: flex;
      align-items: flex-end;

      .card-icon {
        width: 96rpx;
        height: 125rpx;
      }
    }
  }
}
</style>
