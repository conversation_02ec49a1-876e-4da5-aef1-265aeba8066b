<template>
  <view class="user-info-card" @click="handleClick">
    <view class="avatar-container">
      <image
        :src="avatarSrc"
        class="avatar"
        mode="aspectFill"
      />
    </view>
    <view class="user-details">
      <text class="user-name">{{ userInfo.nickName }}</text>
    </view>
    <view class="arrow-container">
      <image
        src="/static/icons/arrow-right.svg"
        class="arrow-icon"
        mode="aspectFit"
      />
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import logoSvg from '@/static/logo.svg'

const emit = defineEmits(['click'])

const props = defineProps({
  userInfo: {
    type: Object,
    required: true
  }
})

// 计算头像，如果没有头像或头像为空则使用默认头像
const avatarSrc = computed(() => {
  const avatar = props.userInfo.avatar
  return (avatar && avatar.trim()) ? avatar : logoSvg
})

const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
// 颜色变量
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-light: #767676;
$border-gradient: linear-gradient(180deg, #f1d292 0%, #d28b23 100%);

.user-info-card {
  display: flex;
  align-items: center;
  gap: 32rpx;
  cursor: pointer;

  .avatar-container {
    width: 134rpx;
    height: 134rpx;
    border-radius: 50%;
    background: $white-color;
    border: 4rpx solid transparent;
    background-image: $border-gradient;
    background-origin: border-box;
    background-clip: content-box, border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .avatar {
      width: 126rpx;
      height: 126rpx;
      border-radius: 50%;
    }
  }

  .user-details {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    flex: 1;

    .user-name {
      font-size: 46rpx;
      font-weight: 500;
      color: $text-dark;
      line-height: 54rpx;
    }

    .user-uid {
      font-size: 30rpx;
      color: $text-light;
      line-height: 35rpx;
    }
  }

  .arrow-container {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .arrow-icon {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
