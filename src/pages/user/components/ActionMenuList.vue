<template>
  <view class="menu-list">
    <MenuListItem
      v-for="(item, index) in menuList"
      :key="index"
      :icon="item.icon"
      :title="item.title"
      @click="$emit('menuClick', index)"
      class="menu-item"
    />
  </view>
</template>

<script setup>
import MenuListItem from './MenuListItem.vue'

defineProps({
  menuList: {
    type: Array,
    default: () => []
  }
})

defineEmits(['menuClick'])
</script>

<style lang="scss" scoped>
$white-color: #ffffff;
$border-gray: #e9e9e9;

@mixin card-style {
  background-color: $white-color;
  border-radius: 20rpx;
}

.menu-list {
  @include card-style;
  overflow: hidden;
  margin-bottom: 80rpx;

  .menu-item {
    border-bottom: 1rpx solid $border-gray;

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>