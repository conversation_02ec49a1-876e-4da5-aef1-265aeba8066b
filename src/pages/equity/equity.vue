<script setup>
import { ref, computed, onMounted } from "vue"
import { onLoad } from '@dcloudio/uni-app'
import CustomNavBar from '@/components/CustomNavBar.vue'
import { getAdoptionList } from '@/api/order'

// 响应式数据
const equityData = ref(null)
const loading = ref(false)

// 从页面参数获取认养数据
onLoad(async (options) => {
  try {
    loading.value = true

    if (options.adoptionId) {
      // 根据adoptionId获取对应的认养记录
      const res = await getAdoptionList({
        pageNum: 1,
        pageSize: 100, // 获取更多数据以便查找
        status: 1
      })

      if (res.rows && res.rows.length > 0) {
        // 查找对应的认养记录
        const targetAdoption = res.rows.find(item => item.id == options.adoptionId)
        if (targetAdoption) {
          equityData.value = targetAdoption
        } else {
          // 如果没找到，使用第一条记录作为默认
          equityData.value = res.rows[0]
        }
      }
    } else {
      // 如果没有传递adoptionId，获取第一条记录
      const res = await getAdoptionList({
        pageNum: 1,
        pageSize: 1,
        status: 1
      })

      if (res.rows && res.rows.length > 0) {
        equityData.value = res.rows[0]
      }
    }
  } catch (error) {
    console.error('获取权益数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
})

// 计算节省金额（这里使用示例逻辑，实际应根据业务需求计算）
const savedAmount = ref('138.00')

// 解析权益信息
const benefitsInfo = computed(() => {
  if (!equityData.value?.benefits) return []

  // 如果 benefits 是字符串，按行分割
  if (typeof equityData.value.benefits === 'string') {
    return equityData.value.benefits.split('\n').filter(item => item.trim())
  }

  // 如果是数组，直接返回
  return equityData.value.benefits || []
})
</script>

<template>
  <view class="equity-page">
    <!-- 自定义导航栏 -->
    <CustomNavBar
      title="认养权益"
      :show-back="true"
      background-color="transparent"
      title-color="#9b3e02"
    />

    <view class="equity-page__banner">
      <view class="equity-page__banner-bg">
        <!-- Removed header elements as per conversion guide -->
      </view>
    </view>
    <view class="equity-card">
      <view class="equity-card__bg">
        <text class="equity-card__title"> 我的权益 </text>
        <view class="equity-card__saving">
          <text class="equity-card__saving-label">已累计为您节省</text>
          <text class="equity-card__saving-amount">{{ savedAmount }}</text>
          <text class="equity-card__saving-unit">元</text>
        </view>
      </view>
    </view>
    <image class="decoration-1" src="/static/images/矩形 701.png" mode="widthFix" />
    <image class="decoration-2" src="/static/images/矩形 702.png" mode="widthFix" />
    <view class="info-container">
      <view class="info-container__header">
        <view class="info-container__header-bg">
          <text class="info-container__title"> 权益包信息 </text>
          <text class="info-container__order-no"> 订单号：{{ equityData?.orderSn || '暂无订单号' }} </text>
        </view>
      </view>
      <view class="info-container__status-badge">
        <text class="info-container__status-text">生效中</text>
      </view>
    </view>
    <view class="details-card-wrapper">
      <view class="details-card">
        <view class="details-card__header">
          <text class="details-card__name">{{ equityData?.fruitTreeName || '果树认养' }}</text>
          <text class="details-card__title">认养权益</text>
        </view>
        <view class="details-card__divider"></view>
        <text
          v-if="equityData?.benefits"
          class="details-card__benefits"
        >
          {{ equityData.benefits }}
        </text>
        <template v-else>
          <text class="details-card__item"> 1.可享受亲自活动（游玩、采摘等）； </text>
          <text class="details-card__item"> 2.可享受冷藏及包邮服务； </text>
          <text class="details-card__item"> 3.化身线上农夫，在家实时云养树； </text>
          <text class="details-card__item"> 4.专属冠名权； </text>
        </template>
      </view>
      <image
        class="details-card__image"
        src="/static/flower-tree.png"
        mode="aspectFit"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.equity-page {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
  min-height: 100vh;

  &__banner {
    position: relative;
    width: 100%;
    height: 516rpx; // 增加高度以适配状态栏和导航栏
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    z-index: 0;
    margin-top: -100rpx; // 向上偏移，让背景延伸到状态栏区域
  }

  &__banner-bg {
    position: relative;
    width: 100%;
    height: 516rpx; // 对应增加高度
    background: linear-gradient(123.52deg, #ffe1ac 5.42%, #fffaf1 42%, #ffedd0 70.59%, #fff8ee 101.9%);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    isolation: isolate;
  }
}

.equity-card {
  position: relative;
  width: 640rpx;
  height: 242rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 3;
  margin: -200rpx auto 0; // 适中的负边距，既避免重叠又保持合适的视觉距离

  &__bg {
    position: relative;
    width: 100%;
    height: 240rpx;
    background: linear-gradient(163deg, #8D5236 22%, #723E28 70%);
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 44rpx 25rpx;
    box-sizing: border-box;

    &::after {
      content: '';
      position: absolute;
      bottom: -15rpx;
      left: 50%;
      transform: translateX(-50%) rotate(45deg);
      width: 30rpx;
      height: 30rpx;
      background-color: #723E28;
      border-radius: 4rpx;
    }
  }

  &__title {
    font-size: 40rpx;
    font-weight: 500;
    line-height: 47rpx;
    color: #ffefce;
    white-space: pre;
  }

  &__saving {
    margin-top: 13rpx;
    display: flex;
    align-items: baseline;
    color: #f4e6ca;
  }

  &__saving-label {
    font-size: 28rpx;
    font-weight: 400;
    white-space: nowrap;
  }

  &__saving-amount {
    font-size: 64rpx;
    font-weight: normal;
    line-height: 90rpx;
    color: #f4e6ca;
    margin: 0 10rpx;
    white-space: nowrap;
  }

  &__saving-unit {
    font-size: 28rpx;
    font-weight: 400;
    white-space: nowrap;
  }
}

.decoration-1 {
  width: 759rpx;
  height: 119rpx;
  z-index: 2;
  position: relative;
  margin: -116rpx auto 0;
}

.decoration-2 {
  width: 100%;
  height: 91rpx;
  z-index: 1;
  position: relative;
  margin: -41rpx 0 0 -2rpx;
}

.info-container {
  width: 686rpx;
  height: 1085rpx;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  z-index: 4;
  margin: -18rpx auto 0;
  border-radius: 16rpx;
  background: linear-gradient(180deg, #fff1cd 0%, #faca7b 98.57%);
  position: relative;

  &__header {
    position: relative;
    width: 539rpx;
    height: 213rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  &__header-bg {
    position: relative;
    width: 100%;
    height: 213rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  &__title {
    font-size: 32rpx;
    font-weight: 500;
    line-height: 38rpx;
    color: #9b3e02;
    white-space: pre;
    position: relative;
    margin: 32rpx 0 0 40rpx;
  }

  &__order-no {
    font-size: 28rpx;
    font-weight: 400;
    line-height: 33rpx;
    color: #c28c50;
    white-space: pre;
    position: relative;
    margin: 20rpx 0 0 40rpx;
  }

  &__status-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #dd3c29;
    border-radius: 0 16rpx 0 16rpx;
    padding: 0 20rpx;
    height: 40rpx;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
  }

  &__status-text {
    color: #ffffff;
    font-size: 24rpx;
    font-weight: 400;
    white-space: nowrap;
  }
}

.details-card-wrapper {
  position: relative;
  width: 654rpx;
  height: 940rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 5;
  margin: -932rpx auto 0;
}

.details-card {
  position: relative;
  width: 100%;
  height: 916rpx;
  border-radius: 12rpx;
  background: linear-gradient(180deg, #ffffff 0%, #fffdf3 99.29%);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
  padding: 32rpx;
  box-sizing: border-box;

  &__header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 27rpx;
  }

  &__name {
    font-size: 32rpx;
    font-weight: 500;
    line-height: 38rpx;
    color: #9b3e02;
    white-space: nowrap;
  }

  &__title {
    font-size: 32rpx;
    font-weight: 500;
    line-height: 38rpx;
    color: #9b3e02;
    white-space: nowrap;
  }

  &__divider {
    width: 590rpx;
    height: 1rpx;
    background-color: #d8d8d8;
    margin: 16rpx auto 0;
    align-self: center;
  }

  &__item {
    font-size: 32rpx;
    font-weight: 400;
    line-height: 38rpx;
    color: #000000;
    white-space: pre;
    margin-top: 36rpx;
  }

  &__benefits {
    font-size: 32rpx;
    font-weight: 400;
    line-height: 32rpx;
    color: #000000;
    white-space: pre-wrap;
    margin-top: 36rpx;
    word-break: break-all;
  }

  &__image {
    width: 400rpx;
    height: 400rpx;
    position: absolute;
    object-fit: cover;
    left: 50%;
    transform: translateX(-50%);
    bottom: 24rpx;
  }

}
</style>
