<script setup>
import { computed } from 'vue';
import { getCashierPayParams } from '@/api/order';
import { useUserStore } from '@/store/user';

const props = defineProps({
  order: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['refresh']);

// 用户store
const userStore = useUserStore();

// The action text depends on the order status
const actionText = computed(() => {
  switch (props.order.status) {
    case '0':
      return '去付款';
    case '20':
      return '确认收货';
    default:
      return null; // No button for other statuses
  }
});

// 处理按钮点击
const handleActionClick = async () => {
  if (props.order.status === '0') {
    // 去付款
    await handlePayment();
  } else if (props.order.status === '20') {
    // 确认收货
    handleConfirmReceipt();
  }
};

// 处理支付
const handlePayment = async () => {
  try {
    // 获取支付参数
    const paymentParams = await getCashierPayParams(props.order.orderSn);

    // 拉起支付
    await uni.requestPayment({
      ...paymentParams.data,
      success: async () => {
        uni.showToast({
          title: '支付成功',
          icon: 'success'
        });

        // 刷新用户信息（更新统计数据）
        try {
          await userStore.refreshUserInfo();
        } catch (error) {
          console.error('刷新用户信息失败:', error);
        }

        // 通知父组件刷新订单列表
        emit('refresh');
      },
      fail: (err) => {
        console.error('支付失败:', err);
        uni.showToast({
          title: '支付失败',
          icon: 'none'
        });
      }
    });
  } catch (error) {
    console.error('获取支付参数失败:', error);
    uni.showToast({
      title: '支付失败',
      icon: 'none'
    });
  }
};

// 处理确认收货
const handleConfirmReceipt = () => {
  uni.showModal({
    title: '确认收货',
    content: '确定已收到商品吗？',
    success: (res) => {
      if (res.confirm) {
        // TODO: 调用确认收货API
        uni.showToast({
          title: '确认收货成功',
          icon: 'success'
        });
        // 通知父组件刷新订单列表
        emit('refresh');
      }
    }
  });
};
</script>

<template>
  <view class="order-item-card">
    <view class="card-body">
      <view class="product-image-container">
        <image
          class="product-image"
          :src="order.treeDetail.imageUrls[0]"
          mode="aspectFill"
        />
      </view>
      <text class="product-main-title">{{ order.treeDetail.name }}</text>
      <text class="product-sub-title">{{ order.treeDetail.description }}</text>
      <view class="actions-container">
        <view class="price-info">
          <text class="price">¥ {{ order.totalPrice.toFixed(2) }}</text>
          <text class="quantity">数量: {{ order.fruitNum }}</text>
        </view>
        <view v-if="actionText" class="action-button" @click="handleActionClick">
          <text class="button-text">{{ actionText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.order-item-card {
  width: 686rpx;
  margin-left: auto;
  margin-right: auto;
}

.card-body {
  position: relative;
  width: 100%;
  height: 615rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 378rpx;
  overflow: hidden;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-main-title {
  position: absolute;
  top: 402rpx;
  left: 32rpx;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #1a1a1a;
  white-space: pre;
  z-index: 1;
}

.product-sub-title {
  position: relative;
  width: 622rpx;
  font-size: 30rpx;
  font-weight: 400;
  line-height: 35rpx;
  color: #999999;
  white-space: pre;
  z-index: 2;
  margin-top: 78rpx;
  margin-left: auto;
  margin-right: auto;
}

.actions-container {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 64rpx;
  z-index: 3;
  margin-top: 28rpx;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.price-info {
  display: flex;
  flex-direction: row;
  align-items: baseline;
}

.price {
  font-size: 36rpx;
  font-weight: 700;
  line-height: 42rpx;
  color: #ea0000;
  white-space: pre;
}

.quantity {
  margin-left: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 33rpx;
  color: #999999;
  white-space: pre;
}

.action-button {
  width: 166rpx;
  height: 64rpx;
  border-radius: 32rpx;
  background-color: #dd3c29;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.button-text {
  font-size: 30rpx;
  font-weight: 400;
  line-height: 35rpx;
  color: #ffffff;
  white-space: pre;
}
</style>