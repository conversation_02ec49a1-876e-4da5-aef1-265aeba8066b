<template>
  <view class="address-edit-page">
    <!-- 主要内容区域 -->
    <view class="main-content">
      <text class="page-title">新增地址</text>

      <!-- 姓名输入 -->
      <view class="form-item">
        <view class="form-row">
          <image class="form-icon" src="/static/icons/user.svg" mode="aspectFit" />
          <text class="form-label">姓名</text>
          <input class="form-input" placeholder-class="form-placeholder" type="text" v-model="formData.contactName" placeholder="请输入姓名" />
        </view>
      </view>

      <!-- 联系电话输入 -->
      <view class="form-item">
        <view class="form-row">
          <image class="form-icon" src="/static/icons/phone.svg" mode="aspectFit" />
          <text class="form-label">联系电话</text>
          <input class="form-input" placeholder-class="form-placeholder" type="number" v-model="formData.phone" placeholder="请输入手机号" />
        </view>
      </view>

      <!-- 地址选择 -->
      <picker mode="region" @change="handleRegionChange">
        <view class="form-item">
          <view class="form-row">
            <image class="form-icon" src="/static/icons/location.svg" mode="aspectFit" />
            <text class="form-label">地址选择</text>
            <text class="form-placeholder" :class="{ 'selected': selectedAddress }">
              {{ selectedAddress || '选择省市区' }}
            </text>
            <image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit" />
          </view>
        </view>
      </picker>

      <!-- 详细地址输入 -->
      <view class="form-item">
        <view class="form-row">
          <view class="form-icon-placeholder"></view>
          <text class="form-label">详细地址</text>
          <input
            class="form-input"
            placeholder-class="form-placeholder"
            type="text"
            v-model="formData.detailAddress"
            placeholder="请输入详细地址"
            :maxlength="200"
          />
        </view>
      </view>

      <!-- 默认地址开关 -->
      <view class="form-item">
        <view class="form-row">
          <view class="form-icon-placeholder"></view>
          <text class="form-label">设为默认地址</text>
          <view class="switch-container">
            <view class="switch-track" :class="{ active: formData.isDefault === 1 }" @click="handleSwitchChange">
              <view class="switch-thumb" :class="{ active: formData.isDefault === 1 }"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-button-container">
      <view class="save-button" @click="handleSave">
        <text class="save-button-text">立即保存</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getAddressDetail, addAddress, updateAddress } from '@/api/address.js'

// 表单数据
const formData = ref({
  id: '',
  contactName: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  detailAddress: '',
  isDefault: 0
})

// 是否为编辑模式
const isEdit = ref(false)

// 地址选择相关
// 地址选择相关
const selectedAddress = computed(() => {
  if (formData.value.province) {
    return `${formData.value.province} ${formData.value.city} ${formData.value.district}`
  }
  return ''
})

// 页面加载
onLoad((options) => {
  if (options.id) {
    isEdit.value = true
    loadAddressData(options.id)
  }
})

// 加载地址数据（编辑模式）
const loadAddressData = async (id) => {
  try {
    const res = await getAddressDetail(id)
    const data = res.data
    formData.value = data
  } catch (error) {
    console.error('加载地址数据失败', error)
    uni.showToast({
      title: '加载地址数据失败',
      icon: 'none'
    })
  }
}

// 地址选择器处理
const handleRegionChange = (e) => {
  const [province, city, district] = e.detail.value
  formData.value.province = province
  formData.value.city = city
  formData.value.district = district
}

// 开关变化处理
const handleSwitchChange = () => {
  formData.value.isDefault = formData.value.isDefault === 1 ? 0 : 1
}

// 表单验证
const validateForm = () => {
  if (!formData.value.contactName.trim()) {
    uni.showToast({
      title: '请输入姓名',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.phone.trim()) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none'
    })
    return false
  }

  // 简单的手机号验证
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(formData.value.phone)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return false
  }

  if (!formData.value.detailAddress.trim()) {
    uni.showToast({
      title: '请输入详细地址',
      icon: 'none'
    })
    return false
  }

  return true
}

// 保存地址
const handleSave = async () => {
  if (!validateForm()) {
    return
  }

  try {
    if (isEdit.value) {
      await updateAddress(formData.value.id, formData.value)
    } else {
      await addAddress(formData.value)
    }

    uni.showToast({
      title: isEdit.value ? '修改成功' : '添加成功',
      icon: 'success'
    })

    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error('保存地址失败', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$bg-gray: #f5f5f5;
$text-primary: #1a1a1a;
$text-secondary: #999999;
$border-color: #eeeeee;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.address-edit-page {
  min-height: 100vh;
  background-color: $bg-gray;
  display: flex;
  flex-direction: column;
}

.main-content {
  background-color: $white-color;
  margin-top: 32rpx;
  padding: 0;
}

.page-title {
  font-size: 32rpx;
  font-weight: 700;
  line-height: 55rpx;
  color: $text-primary;
  padding: 16rpx 32rpx;
}

.form-item {
  border-bottom: 1rpx solid $border-color;

  &:last-child {
    border-bottom: none;
  }
}

.form-row {
  @include flex-between;
  padding: 0 32rpx;
  height: 100rpx;
}

.form-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 19rpx;
}

.form-icon-placeholder {
  width: 32rpx;
  height: 32rpx;
  margin-right: 19rpx;
}

.form-label {
  font-size: 30rpx;
  line-height: 35rpx;
  color: $text-primary;
  margin-right: 24rpx; // 增加标题和内容之间的间距
  min-width: 160rpx; // 确保标题有足够的宽度，避免内容挤压
  flex-shrink: 0; // 防止标题被压缩
}

.form-placeholder {
  font-size: 30rpx;
  line-height: 35rpx;
  color: $text-secondary;
  text-align: right;
  flex: 1;

  &.selected {
    color: $text-primary;
  }
}

.form-input {
  flex: 1;
  font-size: 30rpx;
  line-height: 35rpx;
  color: $text-primary;
  text-align: right;
  height: 100%;
  min-width: 0; // 确保输入框可以收缩
  padding-left: 8rpx; // 增加左侧内边距，避免文字贴边
}

.arrow-icon {
  width: 48rpx;
  height: 48rpx;
  margin-left: 16rpx;
}

.switch-container {
  display: flex;
  align-items: center;
  margin-left: auto; // 将开关推到右侧
}

.switch-track {
  width: 80rpx;
  height: 40rpx;
  background-color: #cecfd0;
  border-radius: 234rpx;
  position: relative;
  transition: background-color 0.3s;

  &.active {
    background-color: $primary-color;
  }
}

.switch-thumb {
  width: 34rpx;
  height: 34rpx;
  background-color: $white-color;
  border-radius: 50%;
  position: absolute;
  top: 3rpx;
  left: 3rpx;
  transition: transform 0.3s;

  &.active {
    transform: translateX(40rpx);
  }
}

.save-button-container {
  margin-top: 60rpx;
  padding: 0 40rpx 60rpx 40rpx;
}

.save-button {
  @include flex-center;
  width: 670rpx;
  height: 88rpx;
  border-radius: 326rpx;
  border: 2rpx solid rgba($primary-color, 0.5);
  background-color: $white-color;
  margin: 0 auto;

  &:active {
    opacity: 0.8;
  }
}

.save-button-text {
  font-size: 32rpx;
  line-height: 38rpx;
  color: $primary-color;
  text-align: center;
}
</style>
