<template>
  <view class="address-list-container">
    <!-- 空状态 -->
    <view class="empty" v-if="!loading && addressList.length === 0">
      <image class="empty-image" src="/static/no-address.png" mode="aspectFit" />
      <text class="empty-title">暂无收货地址</text>
      <text class="empty-description">添加收货地址，让购物更便捷</text>
    </view>

    <block v-else>
      <!-- 地址列表 -->
      <view class="address-list">
        <AddressCard
          v-for="address in addressList"
          :key="address.id"
          :address="address"
          @edit="handleEdit"
          @delete="handleDelete"
          @set-default="handleSetDefault"
          @click="handleSelectAddress(address)"
          class="address-item"
        />
      </view>
      <!-- 加载更多 -->
      <uni-load-more :status="loadMoreStatus" />
    </block>

    <!-- 添加新地址按钮 -->
    <view class="add-button-container">
      <view class="add-button" @click="handleAddAddress">
        <text class="add-button-text">添加新地址</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onShow, onPullDownRefresh, onReachBottom, onLoad } from '@dcloudio/uni-app'
import AddressCard from '@/components/AddressCard.vue'
import { getAddressList, deleteAddress, updateAddress } from '@/api/address.js'

// 响应式数据
const addressList = ref([])
const loading = ref(false)
const loadMoreStatus = ref('more')
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const isSelectMode = ref(false)

// 获取地址列表
const fetchAddressList = async (isRefresh = false) => {
  if (isRefresh) {
    pageNum.value = 1
    addressList.value = []
    hasMore.value = true
  }

  if (!hasMore.value) return

  loading.value = true
  loadMoreStatus.value = 'loading'

  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value
    }
    const res = await getAddressList(params)
    const { rows, total } = res
    addressList.value = isRefresh ? rows : [...addressList.value, ...rows]
    hasMore.value = addressList.value.length < total
    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore'
    pageNum.value++
  } catch (error) {
    console.error('获取地址列表失败', error)
    uni.showToast({
      title: '获取地址列表失败',
      icon: 'none'
    })
    loadMoreStatus.value = 'more'
  } finally {
    loading.value = false
  }
}

// 编辑地址
const handleEdit = (address) => {
  uni.navigateTo({
    url: `/pages/address/edit/edit?id=${address.id}`
  })
}

// 删除地址
const handleDelete = (address) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个收货地址吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteAddress(address.id)
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          fetchAddressList(true) // 重新加载列表
        } catch (error) {
          console.error('删除地址失败', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 设置默认地址
const handleSetDefault = async (address) => {
  if (address.isDefault) return

  try {
    await updateAddress(address.id, { ...address, isDefault: true })
    uni.showToast({
      title: '设置成功',
      icon: 'success'
    })
    fetchAddressList(true) // 重新加载列表
  } catch (error) {
    console.error('设置默认地址失败', error)
    uni.showToast({
      title: '设置失败',
      icon: 'none'
    })
  }
}

// 添加新地址
const handleAddAddress = () => {
  uni.navigateTo({
    url: '/pages/address/edit/edit'
  })
}

// 选择地址
const handleSelectAddress = (address) => {
  if (isSelectMode.value) {
    uni.$emit('addressSelected', address)
    uni.navigateBack()
  }
}

onLoad((options) => {
  if (options.from === 'select') {
    isSelectMode.value = true
    uni.setNavigationBarTitle({
      title: '选择收货地址'
    })
  }
})

onShow(() => {
  fetchAddressList(true)
})

// 下拉刷新
onPullDownRefresh(async () => {
  await fetchAddressList(true)
  uni.stopPullDownRefresh()
})

// 触底加载更多
onReachBottom(() => {
  if (!loading.value && hasMore.value) {
    fetchAddressList()
  }
})
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$bg-gray: #f5f5f5;
$text-primary: #1a1a1a;
$text-secondary: #767676;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.address-list-container {
  min-height: 100vh;
  background-color: $bg-gray;
  padding: 32rpx 32rpx 120rpx 32rpx; // 底部留出按钮空间
}

// AddressCard组件自己处理间距，这里不需要额外设置

.empty {
  @include flex-center;
  flex-direction: column;
  padding: 120rpx 40rpx;
  text-align: center;

  .empty-image {
    width: 400rpx;
    height: 400rpx;
    margin-bottom: 40rpx;
  }

  .empty-title {
    font-size: 36rpx;
    line-height: 42rpx;
    color: $text-primary;
    font-weight: 500;
    margin-bottom: 16rpx;
  }

  .empty-description {
    font-size: 28rpx;
    line-height: 33rpx;
    color: $text-secondary;
  }
}

.add-button-container {
  position: fixed;
  bottom: 40rpx;
  left: 32rpx;
  right: 32rpx;
  z-index: 100;
}

.add-button {
  @include flex-center;
  width: 100%;
  height: 88rpx;
  background-color: $primary-color;
  border-radius: 44rpx;

  &:active {
    opacity: 0.8;
  }

  .add-button-text {
    font-size: 32rpx;
    line-height: 38rpx;
    color: $white-color;
    font-weight: 500;
  }
}
</style>

