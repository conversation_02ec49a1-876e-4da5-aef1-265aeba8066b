<script setup>
const phoneNumber = '***********';

const makePhoneCall = () => {
  uni.makePhoneCall({
    phoneNumber: phoneNumber,
  });
};
</script>

<template>
  <view class="cooperation-page">
    <view class="card">
      <view class="contact-header">
        <view class="logo-wrapper">
          <image class="logo" src="/static/business-cooperation.svg" mode="aspectFit" />
        </view>
        <view class="contact-details">
          <text class="company-name">宜川三物公司</text>
          <text class="phone-number" @click="makePhoneCall">{{ phoneNumber }}</text>
        </view>
      </view>
      <image class="qr-code" src="/static/contact-qr.png" mode="aspectFit" :show-menu-by-longpress="true" />
      <text class="caption">保存上面的二维码，微信扫一扫添加好友</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.cooperation-page {
  box-sizing: border-box;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card {
  width: 100%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  padding: 102rpx 80rpx 100rpx;
  box-sizing: border-box;
}

.contact-header {
  display: flex;
  align-items: center;
  margin-bottom: 35rpx;
}

.logo-wrapper {
  width: 104rpx;
  height: 104rpx;
  border-radius: 12rpx;
  background-color: #ffffff; // Assuming it's on a white background, so no shadow needed
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx; // Spacing between logo and text
}

.logo {
  width: 70rpx;
  height: 77rpx;
}

.contact-details {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 18rpx;
}

.phone-number {
  font-size: 24rpx;
  font-weight: 400;
  color: #007aff;
  cursor: pointer;
}

.qr-code {
  width: 590rpx;
  height: 588rpx;
  margin-bottom: 64rpx;
  align-self: center;
}

.caption {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  align-self: center;
}
</style>