# no-address.png 图片说明

## 图片要求

**文件名**: `no-address.png`  
**位置**: `/src/static/no-address.png`  
**尺寸**: 240px × 240px (在页面中显示为240rpx × 240rpx)  
**格式**: PNG（支持透明背景）  

## 设计要求

### 视觉风格
- 简洁、友好的插画风格
- 与MyTree项目整体设计风格保持一致
- 使用柔和的颜色，避免过于鲜艳

### 内容建议
- 可以是一个简单的地址图标或位置标记
- 可以是一个空的地址卡片示意图
- 可以是一个房子或地图的简化图标
- 建议使用灰色调，体现"空"的状态

### 颜色建议
- 主色调：#f0f0f0 或 #e5e5e5（浅灰色）
- 辅助色：#cccccc（中灰色）
- 避免使用过于鲜艳的颜色

## 使用场景

这个图片将在以下情况显示：
- 用户首次进入地址列表页面，还没有添加任何地址
- 用户删除了所有地址后的空状态
- 配合文字"暂无收货地址"和"添加收货地址，让购物更便捷"

## 临时解决方案

在正式图片到位之前，可以：
1. 使用项目中现有的图标作为占位符
2. 使用纯色背景的简单图形
3. 暂时隐藏图片，只显示文字

## 代码中的使用

```vue
<image class="empty-image" src="/static/no-address.png" mode="aspectFit" />
```

样式设置：
```scss
.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 40rpx;
}
```
