import HttpRequest from 'luch-request'

import config from '@/config'

const http = new HttpRequest()

/**
 * @typedef {object} LoginResponse
 * @property {string} token
 * @property {string} expiresTime
 */

/**
 * 微信授权登录
 * @param {string} code - 微信登录凭证
 * @returns {Promise<LoginResponse>}
 */
export function wxLogin(code) {
  const url = config.baseURL + '/auth/wx-login'
  return http.request({
    url,
    method: 'POST',
    data: { code }
  })
}
