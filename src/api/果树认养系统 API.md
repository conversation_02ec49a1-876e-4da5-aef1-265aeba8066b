---
title: 果树认养系统 API
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 果树认养系统 API

Base URLs:

# Authentication

* API Key (Authorization)
    - Parameter Name: **Authorization**, in: header. 

# 授权

## POST 静默登录

POST /auth/wx-login

微信登录

> Body 请求参数

```json
{
  "code": "wx_auth_code_123"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» code|body|string| 是 |微信授权码|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresTime": "2023-01-01T12:00:00Z"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» token|string|true|none||none|
|»» expiresTime|integer|true|none|时间戳，单位毫秒|none|

## POST 绑定手机号

POST /auth/bind-mobile

绑定手机号。前端获取授权code ,后端拿 code 去微信开放接口，获取到手机号，然后更新表里用户信息

> Body 请求参数

```json
{
  "code": "xxxx"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» code|body|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "绑定成功"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

# 用户

## GET 获取用户信息

GET /users/profile

获取用户信息

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "获取用户信息成功",
  "data": {
    "userId": "user_123",
    "nickname": "果树达人",
    "avatar": "https://cdn.example.com/avatars/user_123/avatar.png",
    "phone": "170****8017",
    "stats": {
      "adoptedTreesCount": 1,
      "claimedBenefitsCount": 1
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» userId|string|true|none||none|
|»» nickname|string|true|none||none|
|»» avatar|string|true|none||none|
|»» phone|string|true|none||none|
|»» stats|object|true|none||none|
|»»» adoptedTreesCount|integer|true|none||none|
|»»» claimedBenefitsCount|integer|true|none||none|

## PUT 编辑用户信息

PUT /users/profile

更新用户信息

> Body 请求参数

```json
{
  "nickname": "李四",
  "avatar": "https://example.com/new_avatar.jpg"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» nickname|body|string| 否 |none|
|» avatar|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "更新成功"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

# 果树

## GET 获取果树列表

GET /trees

获取果树列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNum|query|integer| 否 |页码|
|pageSize|query|integer| 否 |每页数量|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "获取列表成功",
  "rows": [
    {
      "id": "tree_1024",
      "name": "产品名称01",
      "imageUrl": "https://cdn.example.com/images/apple_tree_sun.jpg",
      "description": "此处为产品的简单介绍，给用户提供简单认知",
      "price": 198,
      "stock": 1669
    },
    {
      "id": "tree_1025",
      "name": "产品名称02",
      "imageUrl": "https://cdn.example.com/images/peach_tree.jpg",
      "description": "这是另一个果树产品的介绍，非常诱人。",
      "price": 258,
      "stock": 890
    }
  ],
  "total": 56
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» rows|[object]|true|none||none|
|»» id|string|true|none||none|
|»» name|string|true|none||none|
|»» imageUrl|string|true|none||none|
|»» description|string|true|none||none|
|»» price|integer|true|none||none|
|»» stock|integer|true|none||none|
|» total|integer|true|none||none|

## GET 获取果树详情

GET /trees/{id}

根据ID获取果树详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "获取产品详情成功",
  "data": {
    "id": "tree_1024",
    "name": "产品名称01",
    "description": "此处为产品的简单介绍，给用户提供简单认知",
    "imageUrls": [
      "https://cdn.example.com/images/apple/1.jpg",
      "https://cdn.example.com/images/apple/2.jpg",
      "https://cdn.example.com/images/apple/3.jpg"
    ],
    "price": 198,
    "salesCount": 166,
    "benefits": [
      {
        "icon": "clock_icon_url",
        "title": "认养权益",
        "text": "果子成熟将收到2箱24枚80#果径一级果"
      },
      {
        "icon": "heart_icon_url",
        "title": "坏果赔付",
        "text": "受到坏果后即时赔付"
      }
    ],
    "details": {
      "brand": "金壶口",
      "productType": "食用农产品",
      "diameter": "80mm-85mm",
      "shelfLife": "15天",
      "origin": "陕西·延安·壶口",
      "packaging": "礼盒装",
      "storage": "建议冷藏",
      "variety": "红富士",
      "shippingTime": "应季水果(每年11月初现摘现发)",
      "adoptionPeriod": "一年一季",
      "sunshineHours": "年日照2500小时以上"
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|
|»» name|string|true|none||none|
|»» description|string|true|none||none|
|»» imageUrls|[string]|true|none||none|
|»» price|integer|true|none||none|
|»» salesCount|integer|true|none||none|
|»» benefits|[object]|true|none||none|
|»»» icon|string|true|none||none|
|»»» title|string|true|none||none|
|»»» text|string|true|none||none|
|»» details|object|true|none||none|
|»»» brand|string|true|none||none|
|»»» productType|string|true|none||none|
|»»» diameter|string|true|none||none|
|»»» shelfLife|string|true|none||none|
|»»» origin|string|true|none||none|
|»»» packaging|string|true|none||none|
|»»» storage|string|true|none||none|
|»»» variety|string|true|none||none|
|»»» shippingTime|string|true|none||none|
|»»» adoptionPeriod|string|true|none||none|
|»»» sunshineHours|string|true|none||none|

# 订单

## GET 获取订单列表

GET /orders

获取订单列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNum|query|integer| 否 |页码|
|pageSize|query|integer| 否 |每页数量|
|status|query|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "获取成功",
  "rows": [
    {
      "id": 1,
      "userId": 1,
      "fruitTreeId": 1,
      "orderNo": "ORD202301010001",
      "status": "paid",
      "adoptionStatus": "growing",
      "totalAmount": 299,
      "addressId": 1
    },
    {
      "id": 2,
      "userId": 1,
      "fruitTreeId": 2,
      "orderNo": "ORD202301010002",
      "status": "pending",
      "totalAmount": 399
    }
  ],
  "total": 15
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» rows|[[Order](#schemaorder)]|true|none||none|
|»» id|integer|true|none||none|
|»» userId|integer|true|none||none|
|»» fruitTreeId|integer|true|none||none|
|»» orderNo|string|true|none||none|
|»» status|string|true|none||none|
|»» adoptionStatus|string|true|none||none|
|»» totalAmount|number|true|none||none|
|»» addressId|integer|true|none||none|
|»» payTime|string(date-time)|false|none||none|
|»» shipTime|string(date-time)|false|none||none|
|»» completeTime|string(date-time)|false|none||none|
|»» createdAt|string(date-time)|true|none||none|
|»» updatedAt|string(date-time)|true|none||none|
|» total|integer|true|none||none|

## POST 创建订单

POST /orders

创建订单

> Body 请求参数

```json
{
  "productId": "tree_1024",
  "quantity": 1,
  "addressId": "addr_7a9f4e21",
  "plaqueName": "王小明的苹果树",
  "message": "希望它茁壮成长！"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» productId|body|string| 是 |none|
|» quantity|body|integer| 是 |none|
|» addressId|body|string| 是 |none|
|» plaqueName|body|string| 是 |none|
|» message|body|string| 是 |none|

> 返回示例

> 201 Response

```json
{
  "code": 200,
  "msg": "订单创建成功",
  "data": {
    "orderId": "20231027155011001"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|none|Inline|

### 返回数据结构

状态码 **201**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» orderId|string|true|none||none|

## POST 获取支付参数

POST /orders/payments

> Body 请求参数

```json
{
  "orderId": "20231027155011001"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 是 |none|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» orderId|body|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "支付参数获取成功",
  "data": {
    "paymentParams": {
      "appId": "wx_...",
      "timeStamp": "1698393011",
      "nonceStr": "aRandomString",
      "package": "prepay_id=...",
      "signType": "RSA",
      "paySign": "aGeneratedSignatureString..."
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» paymentParams|object|true|none||none|
|»»» appId|string|true|none||none|
|»»» timeStamp|string|true|none||none|
|»»» nonceStr|string|true|none||none|
|»»» package|string|true|none||none|
|»»» signType|string|true|none||none|
|»»» paySign|string|true|none||none|

# 地址

## GET 获取地址列表

GET /addresses

获取用户地址列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageSize|query|string| 否 |none|
|pageNum|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "获取地址列表成功",
  "rows": [
    {
      "id": "addr_1a2b3c",
      "contactName": "王先生",
      "phone": "170****8017",
      "province": "江苏省",
      "city": "南京市",
      "district": "江宁区",
      "detailedAddress": "淳化街道开源路9号",
      "isDefault": true
    },
    {
      "id": "addr_4d5e6f",
      "contactName": "王先生",
      "phone": "170****8017",
      "province": "江苏省",
      "city": "南京市",
      "district": "江宁区",
      "detailedAddress": "淳化街道开源路9号",
      "isDefault": false
    }
  ],
  "total": 2
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» rows|[object]|true|none||none|
|»» id|string|true|none||none|
|»» contactName|string|true|none||none|
|»» phone|string|true|none||none|
|»» province|string|true|none||none|
|»» city|string|true|none||none|
|»» district|string|true|none||none|
|»» detailedAddress|string|true|none||none|
|»» isDefault|boolean|true|none||none|
|» total|integer|true|none||none|

## POST 新增地址

POST /addresses

创建地址

> Body 请求参数

```json
{
  "contactName": "李女士",
  "phone": "18812345678",
  "province": "上海市",
  "city": "上海市",
  "district": "浦东新区",
  "detailedAddress": "张江高科XX路100号",
  "isDefault": false
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» contactName|body|string| 是 |none|
|» phone|body|string| 是 |none|
|» province|body|string| 是 |none|
|» city|body|string| 是 |none|
|» district|body|string| 是 |none|
|» detailedAddress|body|string| 是 |none|
|» isDefault|body|boolean| 是 |none|

> 返回示例

> 201 Response

```json
{
  "code": 201,
  "msg": "添加地址成功"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|none|Inline|

### 返回数据结构

状态码 **201**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

## GET 获取地址详情

GET /addresses/{id}

根据ID获取地址详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 51,
  "message": "in cupidatat in incididunt labore",
  "data": {
    "id": "86IiPOf1HXs4gKvBJiL7t",
    "province": "湖北省",
    "city": "衡京市",
    "district": "哈巴河县",
    "detail": "Lorem",
    "consigneeName": "堵俊凯",
    "consigneePhone": "07045932303",
    "isDefault": true
  },
  "msg": "ullamco in quis consectetur elit"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|
|»» province|string|true|none||none|
|»» city|string|true|none||none|
|»» district|string|true|none||none|
|»» detail|string|true|none||none|
|»» consigneeName|string|true|none||none|
|»» consigneePhone|string|true|none||none|
|»» isDefault|boolean|true|none||none|
|» msg|string|true|none||none|

## PUT 编辑地址

PUT /addresses/{id}

更新地址信息

> Body 请求参数

```json
{
  "id": "addr_1704067200123",
  "province": "广东省",
  "city": "深圳市",
  "district": "南山区",
  "detail": "科技园南区深南大道10000号腾讯大厦A座18楼",
  "consigneeName": "张三",
  "consigneePhone": "13800138000",
  "isDefault": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string| 是 |none|
|body|body|object| 否 |none|
|» contactName|body|string| 是 |none|
|» phone|body|string| 是 |none|
|» province|body|string| 是 |none|
|» city|body|string| 是 |none|
|» district|body|string| 是 |none|
|» detailedAddress|body|string| 是 |none|
|» isDefault|body|boolean| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "更新成功"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

## DELETE 删除地址

DELETE /addresses/{id}

删除地址

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "删除成功"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

# 上传

## POST 文件上传，用来上传头像

POST /upload

单文件上传

> Body 请求参数

```yaml
file: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file|body|file| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "上传成功",
  "data": {
    "url": "https://example.com/uploads/abc123.jpg",
    "originalName": "photo.jpg",
    "size": 1024000
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» url|string|true|none||none|
|»» originalName|string|true|none||none|
|»» size|integer|true|none||none|

# 认养

## GET 获取认养列表

GET /adoptions

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNum|query|string| 否 |none|
|pageSize|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "获取认养列表成功",
  "rows": [
    {
      "id": "adopt_12345",
      "title": "树枝枝系列·套餐A",
      "subtitle": "套餐A: 2箱24枚80#果径一级果",
      "imageUrl": "https://cdn.example.com/images/apple_tree.jpg",
      "purchaseDate": "2024-12-10",
      "expectedHarvestDate": "2025-12-10",
      "progress": {
        "percentage": 61,
        "text": "已领养 35周 距离目标越来越近了",
        "secondaryText": "让静待花开，等待收获的美满和小确幸"
      },
      "certificateId": "JHKC202505101212",
      "benefitsPackage": {
        "orderNumber": "23243243545",
        "title": "4项特权",
        "items": [
          {
            "description": "可享受亲自活动（游玩、采摘等）"
          },
          {
            "description": "可享受冷藏及包邮服务"
          },
          {
            "description": "化身线上农夫，在家实时云养树"
          },
          {
            "description": "专属冠名权"
          }
        ]
      }
    }
  ],
  "total": 1
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» rows|[object]|true|none||none|
|»»» id|string|false|none||none|
|»»» title|string|false|none||none|
|»»» subtitle|string|false|none||none|
|»»» imageUrl|string|false|none||none|
|»»» purchaseDate|string|false|none||none|
|»»» expectedHarvestDate|string|false|none||none|
|»»» progress|object|false|none||none|
|»»»» percentage|integer|true|none||none|
|»»»» text|string|true|none||none|
|»»»» secondaryText|string|true|none||none|
|»»» certificateId|string|false|none||none|
|»»» benefitsPackage|object|false|none||none|
|»»»» orderNumber|string|true|none||none|
|»»»» title|string|true|none||none|
|»»»» items|[object]|true|none||none|
|»»»»» description|string|true|none||none|
|»» total|integer|true|none||none|

# 果树订单管理

<a id="opIdaddUsingPOST"></a>

## POST 新增订单信息

POST /swagger-ui.html/system/fruitOrder

> Body 请求参数

```json
{
  "mobile": "string",
  "password": "string",
  "userId": 0,
  "username": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|[UserEntity](#schemauserentity)| 否 | UserEntity|none|

> 返回示例

> 200 Response

```
{"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» **additionalProperties**|object|false|none||none|

<a id="opIdeditUsingPUT"></a>

## PUT 修改订单信息

PUT /swagger-ui.html/system/fruitOrder

> Body 请求参数

```json
{
  "mobile": "string",
  "password": "string",
  "userId": 0,
  "username": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|[UserEntity](#schemauserentity)| 否 | UserEntity|none|

> 返回示例

> 200 Response

```
{"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» **additionalProperties**|object|false|none||none|

<a id="opIdexportUsingPOST"></a>

## POST 导出订单信息列表

POST /swagger-ui.html/system/fruitOrder/export

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|addressId|query|integer(int64)| 否 ||收货地址ID|
|adoptBy|query|string| 否 ||收养者|
|adoptTime|query|string(date-time)| 否 ||认养时间|
|createBy|query|string| 否 ||none|
|createTime|query|string(date-time)| 否 ||none|
|delFlag|query|string| 否 ||是否删除|
|fruitTreeId|query|integer(int64)| 否 ||果树ID|
|id|query|integer(int64)| 否 ||id|
|params|query|string| 否 ||none|
|remark|query|string| 否 ||none|
|searchValue|query|string| 否 ||none|
|status|query|string| 否 ||订单状态|
|updateBy|query|string| 否 ||修改者|
|updateTime|query|string(date-time)| 否 ||修改时间|
|userId|query|integer(int64)| 否 ||用户ID|

> 返回示例

> 200 Response

```
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdremoveUsingDELETE"></a>

## DELETE 订单信息

DELETE /swagger-ui.html/system/fruitOrder/{ids}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|path|string| 是 ||ids|

> 返回示例

> 200 Response

```
{"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|204|[No Content](https://tools.ietf.org/html/rfc7231#section-6.3.5)|No Content|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» **additionalProperties**|object|false|none||none|

<a id="opIdgetInfoUsingGET"></a>

## GET 获取订单信息详细信息

GET /swagger-ui.html/system/fruitOrder/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|integer(int64)| 是 ||id|

> 返回示例

> 200 Response

```
{"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» **additionalProperties**|object|false|none||none|

# 果树信息

<a id="opIdeditUsingPUT_1"></a>

## PUT 修改果树信息

PUT /swagger-ui.html/system/fruitTree

> Body 请求参数

```json
{
  "brand": "string",
  "createBy": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "description": "string",
  "diameter": "string",
  "id": 0,
  "image": "string",
  "name": "string",
  "origin": "string",
  "params": {},
  "price": 0.1,
  "productType": "string",
  "remark": "string",
  "shelflife": "string",
  "soldStock": 0,
  "specPackage": "string",
  "storageCondition": "string",
  "totalStock": 0,
  "updateBy": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "variety": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|[FruitTree](#schemafruittree)| 否 | FruitTree|none|

> 返回示例

> 200 Response

```
{"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» **additionalProperties**|object|false|none||none|

<a id="opIdaddUsingPOST_1"></a>

## POST 果树信息

POST /swagger-ui.html/system/fruitTree/add

> Body 请求参数

```json
{
  "brand": "string",
  "createBy": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "description": "string",
  "diameter": "string",
  "id": 0,
  "image": "string",
  "name": "string",
  "origin": "string",
  "params": {},
  "price": 0.1,
  "productType": "string",
  "remark": "string",
  "shelflife": "string",
  "soldStock": 0,
  "specPackage": "string",
  "storageCondition": "string",
  "totalStock": 0,
  "updateBy": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "variety": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|[FruitTree](#schemafruittree)| 否 | FruitTree|none|

> 返回示例

> 200 Response

```
{"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» **additionalProperties**|object|false|none||none|

<a id="opIdexportUsingPOST_1"></a>

## POST 导出果树信息列表

POST /swagger-ui.html/system/fruitTree/export

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|brand|query|string| 否 ||none|
|createBy|query|string| 否 ||none|
|createTime|query|string(date-time)| 否 ||none|
|description|query|string| 否 ||none|
|diameter|query|string| 否 ||none|
|id|query|integer(int64)| 否 ||none|
|image|query|string| 否 ||none|
|name|query|string| 否 ||none|
|origin|query|string| 否 ||none|
|params|query|string| 否 ||none|
|price|query|number(double)| 否 ||none|
|productType|query|string| 否 ||none|
|remark|query|string| 否 ||none|
|searchValue|query|string| 否 ||none|
|shelflife|query|string| 否 ||none|
|soldStock|query|integer(int32)| 否 ||none|
|specPackage|query|string| 否 ||none|
|storageCondition|query|string| 否 ||none|
|totalStock|query|integer(int32)| 否 ||none|
|updateBy|query|string| 否 ||none|
|updateTime|query|string(date-time)| 否 ||none|
|variety|query|string| 否 ||none|

> 返回示例

> 200 Response

```
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdlistUsingGET_1"></a>

## GET 查询果树信息列表

GET /swagger-ui.html/system/fruitTree/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|brand|query|string| 否 ||none|
|createBy|query|string| 否 ||none|
|createTime|query|string(date-time)| 否 ||none|
|description|query|string| 否 ||none|
|diameter|query|string| 否 ||none|
|id|query|integer(int64)| 否 ||none|
|image|query|string| 否 ||none|
|name|query|string| 否 ||none|
|origin|query|string| 否 ||none|
|params|query|string| 否 ||none|
|price|query|number(double)| 否 ||none|
|productType|query|string| 否 ||none|
|remark|query|string| 否 ||none|
|searchValue|query|string| 否 ||none|
|shelflife|query|string| 否 ||none|
|soldStock|query|integer(int32)| 否 ||none|
|specPackage|query|string| 否 ||none|
|storageCondition|query|string| 否 ||none|
|totalStock|query|integer(int32)| 否 ||none|
|updateBy|query|string| 否 ||none|
|updateTime|query|string(date-time)| 否 ||none|
|variety|query|string| 否 ||none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","rows":[{}],"total":0}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfo](#schematabledatainfo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

*TableDataInfo*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer(int32)|false|none||none|
|» msg|string|false|none||none|
|» rows|[object]|false|none||none|
|» total|integer(int64)|false|none||none|

<a id="opIdremoveUsingDELETE_1"></a>

## DELETE 删除果树信息

DELETE /swagger-ui.html/system/fruitTree/{ids}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|ids|path|string| 是 ||ids|

> 返回示例

> 200 Response

```
{"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|204|[No Content](https://tools.ietf.org/html/rfc7231#section-6.3.5)|No Content|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» **additionalProperties**|object|false|none||none|

<a id="opIdgetInfoUsingGET_1"></a>

## GET 获取果树信息详细信息

GET /swagger-ui.html/system/fruitTree/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|integer(int32)| 是 ||id|

> 返回示例

> 200 Response

```
{"property1":{},"property2":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» **additionalProperties**|object|false|none||none|

# 数据模型

<h2 id="tocS_Error">Error</h2>

<a id="schemaerror"></a>
<a id="schema_Error"></a>
<a id="tocSerror"></a>
<a id="tocserror"></a>

```json
{
  "code": 400,
  "msg": "参数错误"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|true|none||none|
|msg|string|true|none||none|

<h2 id="tocS_R«List«UserEntity»»">R«List«UserEntity»»</h2>

<a id="schemar«list«userentity»»"></a>
<a id="schema_R«List«UserEntity»»"></a>
<a id="tocSr«list«userentity»»"></a>
<a id="tocsr«list«userentity»»"></a>

```json
{
  "code": 0,
  "data": [
    {
      "mobile": "string",
      "password": "string",
      "userId": 0,
      "username": "string"
    }
  ],
  "msg": "string"
}

```

R«List«UserEntity»»

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[UserEntity](#schemauserentity)]|false|none||[用户实体]|
|msg|string|false|none||none|

<h2 id="tocS_Address">Address</h2>

<a id="schemaaddress"></a>
<a id="schema_Address"></a>
<a id="tocSaddress"></a>
<a id="tocsaddress"></a>

```json
{
  "id": 1,
  "userId": 1,
  "consigneeName": "张三",
  "consigneeMobile": "13800138000",
  "region": "北京市朝阳区",
  "detailAddress": "某某街道123号",
  "isDefault": true,
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|true|none||none|
|userId|integer|true|none||none|
|consigneeName|string|true|none||none|
|consigneeMobile|string|true|none||none|
|region|string|true|none||none|
|detailAddress|string|true|none||none|
|isDefault|boolean|true|none||none|
|createdAt|string(date-time)|true|none||none|
|updatedAt|string(date-time)|true|none||none|

<h2 id="tocS_R«UserEntity»">R«UserEntity»</h2>

<a id="schemar«userentity»"></a>
<a id="schema_R«UserEntity»"></a>
<a id="tocSr«userentity»"></a>
<a id="tocsr«userentity»"></a>

```json
{
  "code": 0,
  "data": {
    "mobile": "string",
    "password": "string",
    "userId": 0,
    "username": "string"
  },
  "msg": "string"
}

```

R«UserEntity»

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[UserEntity](#schemauserentity)|false|none||用户实体|
|msg|string|false|none||none|

<h2 id="tocS_FruitTree">FruitTree</h2>

<a id="schemafruittree"></a>
<a id="schema_FruitTree"></a>
<a id="tocSfruittree"></a>
<a id="tocsfruittree"></a>

```json
{
  "brand": "string",
  "createBy": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "description": "string",
  "diameter": "string",
  "id": 0,
  "image": "string",
  "name": "string",
  "origin": "string",
  "params": {},
  "price": 0.1,
  "productType": "string",
  "remark": "string",
  "shelflife": "string",
  "soldStock": 0,
  "specPackage": "string",
  "storageCondition": "string",
  "totalStock": 0,
  "updateBy": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "variety": "string"
}

```

FruitTree

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|brand|string|false|none||none|
|createBy|string|false|none||none|
|createTime|string(date-time)|false|none||none|
|description|string|false|none||none|
|diameter|string|false|none||none|
|id|integer(int64)|false|none||none|
|image|string|false|none||none|
|name|string|false|none||none|
|origin|string|false|none||none|
|params|object|false|none||none|
|price|number(double)|false|none||none|
|productType|string|false|none||none|
|remark|string|false|none||none|
|shelflife|string|false|none||none|
|soldStock|integer(int32)|false|none||none|
|specPackage|string|false|none||none|
|storageCondition|string|false|none||none|
|totalStock|integer(int32)|false|none||none|
|updateBy|string|false|none||none|
|updateTime|string(date-time)|false|none||none|
|variety|string|false|none||none|

<h2 id="tocS_R«string»">R«string»</h2>

<a id="schemar«string»"></a>
<a id="schema_R«string»"></a>
<a id="tocSr«string»"></a>
<a id="tocsr«string»"></a>

```json
{
  "code": 0,
  "data": "string",
  "msg": "string"
}

```

R«string»

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|string|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_TreeLog">TreeLog</h2>

<a id="schematreelog"></a>
<a id="schema_TreeLog"></a>
<a id="tocStreelog"></a>
<a id="tocstreelog"></a>

```json
{
  "id": 1,
  "orderId": 1,
  "title": "果树开花啦",
  "content": "您的苹果树已经开始开花，长势良好",
  "images": [
    "https://example.com/flower1.jpg",
    "https://example.com/flower2.jpg"
  ],
  "logDate": "2023-03-15",
  "createdAt": "2023-03-15T00:00:00Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|true|none||none|
|orderId|integer|true|none||none|
|title|string|true|none||none|
|content|string|false|none||none|
|images|[string]|false|none||none|
|logDate|object|true|none||none|
|createdAt|string(date-time)|true|none||none|

<h2 id="tocS_TableDataInfo">TableDataInfo</h2>

<a id="schematabledatainfo"></a>
<a id="schema_TableDataInfo"></a>
<a id="tocStabledatainfo"></a>
<a id="tocstabledatainfo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "rows": [
    {}
  ],
  "total": 0
}

```

TableDataInfo

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|rows|[object]|false|none||none|
|total|integer(int64)|false|none||none|

<h2 id="tocS_User">User</h2>

<a id="schemauser"></a>
<a id="schema_User"></a>
<a id="tocSuser"></a>
<a id="tocsuser"></a>

```json
{
  "id": 1,
  "openid": "oXxXxXxXxXxXxXxXxXxXxXxXxXxXx",
  "nickname": "张三",
  "avatar": "https://example.com/avatar.jpg",
  "mobile": "13800138000",
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|true|none||none|
|openid|string|true|none||none|
|nickname|string|false|none||none|
|avatar|string|false|none||none|
|mobile|string|false|none||none|
|createdAt|string(date-time)|true|none||none|
|updatedAt|string(date-time)|true|none||none|

<h2 id="tocS_UserEntity">UserEntity</h2>

<a id="schemauserentity"></a>
<a id="schema_UserEntity"></a>
<a id="tocSuserentity"></a>
<a id="tocsuserentity"></a>

```json
{
  "mobile": "string",
  "password": "string",
  "userId": 0,
  "username": "string"
}

```

UserEntity

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|mobile|string|false|none||用户手机|
|password|string|false|none||用户密码|
|userId|integer(int32)|false|none||用户ID|
|username|string|false|none||用户名称|

<h2 id="tocS_Order">Order</h2>

<a id="schemaorder"></a>
<a id="schema_Order"></a>
<a id="tocSorder"></a>
<a id="tocsorder"></a>

```json
{
  "id": 1,
  "userId": 1,
  "fruitTreeId": 1,
  "orderNo": "ORD202301010001",
  "status": "paid",
  "adoptionStatus": "growing",
  "totalAmount": 299,
  "addressId": 1,
  "payTime": "2023-01-01T10:00:00Z",
  "shipTime": "2023-01-02T10:00:00Z",
  "completeTime": null,
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|true|none||none|
|userId|integer|true|none||none|
|fruitTreeId|integer|true|none||none|
|orderNo|string|true|none||none|
|status|string|true|none||none|
|adoptionStatus|string|true|none||none|
|totalAmount|number|true|none||none|
|addressId|integer|true|none||none|
|payTime|string(date-time)|false|none||none|
|shipTime|string(date-time)|false|none||none|
|completeTime|string(date-time)|false|none||none|
|createdAt|string(date-time)|true|none||none|
|updatedAt|string(date-time)|true|none||none|

<h2 id="tocS_ApiResponse">ApiResponse</h2>

<a id="schemaapiresponse"></a>
<a id="schema_ApiResponse"></a>
<a id="tocSapiresponse"></a>
<a id="tocsapiresponse"></a>

```json
{
  "code": 200,
  "msg": "success",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|true|none||none|
|msg|string|true|none||none|
|data|any|false|none||none|

