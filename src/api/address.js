import request from '@/libs/http'

/**
 * @typedef {object} UserAddress
 * @property {number} id
 * @property {string} contactName
 * @property {string} phone
 * @property {string} province
 * @property {string} city
 * @property {string} district
 * @property {string} detailedAddress
 * @property {boolean} isDefault
 */

/**
 * @typedef {object} AddressListResponse
 * @property {UserAddress[]} rows
 * @property {number} total
 */

/**
 * 获取地址列表
 * @param {object} [params]
 * @param {string} [params.pageNum]
 * @param {string} [params.pageSize]
 * @returns {Promise<AddressListResponse>}
 */
export function getAddressList(params) {
  return request({
    url: '/address',
    method: 'GET',
    params
  })
}

/**
 * 新增地址
 * @param {object} data
 * @param {string} data.contactName
 * @param {string} data.phone
 * @param {string} data.province
 * @param {string} data.city
 * @param {string} data.district
 * @param {string} data.detailedAddress
 * @param {boolean} data.isDefault
 * @returns {Promise<UserAddress>}
 */
export function addAddress(data) {
  return request({
    url: '/address',
    method: 'POST',
    data
  })
}

/**
 * 获取地址详情
 * @param {number} id
 * @returns {Promise<UserAddress>}
 */
export function getAddressDetail(id) {
  return request({
    url: `/address/${id}`,
    method: 'GET'
  })
}

/**
 * 修改地址
 * @param {number} id
 * @param {object} data
 * @returns {Promise<UserAddress>}
 */
export function updateAddress(id, data) {
  return request({
    url: `/address/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除地址
 * @param {number} id
 * @returns {Promise<void>}
 */
export function deleteAddress(id) {
  return request({
    url: `/address/${id}`,
    method: 'DELETE'
  })
}