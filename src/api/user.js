import request from '@/libs/http'
import { useUserStore } from '@/store/user'
import config from "@/config";


/**
 * 绑定手机号
 * @param {string} code - 微信授权码
 * @returns {Promise<void>}
 */
export function bindMobile(code) {
  return request({
    url: '/auth/bind-mobile',
    method: 'POST',
    data: { code }
  })
}

/**
 * @typedef {object} UserStats
 * @property {number} adoptedTreesCount
 * @property {number} claimedBenefitsCount
 */

/**
 * @typedef {object} User
 * @property {string} userId
 * @property {string} nickName
 * @property {string} avatar
 * @property {string} phonenumber
 * @property {UserStats} stats
 */

/**
 * 获取用户信息
 * @returns {Promise<User>}
 */
export function getProfile() {
  return request({
    url: '/users/profile',
    method: 'GET'
  })
}



/**
 * 编辑用户信息
 * @param {object} data
 * @param {string} [data.nickName]
 * @param {string} [data.avatar]
 * @returns {Promise<void>}
 */
export function updateProfile(data) {
  return request({
    url: '/users/profile',
    method: 'PUT',
    data
  })
}

/**
 * @typedef {object} UploadFileResponse
 * @property {string} url
 * @property {string} originalName
 * @property {number} size
 */

/**
 * 上传文件
 * @param {File} file - 要上传的文件
 * @returns {Promise<UploadFileResponse>}
 */
export function uploadFile(file) {
  // uni.uploadFile 不经过 request 拦截器，所以这里需要单独处理
  // Content-Type 为 multipart/form-data
  const userStore = useUserStore()
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: config.baseURL + '/common/upload',
      filePath: file.path,
      name: 'file',
      header: {
        Authorization: 'Bearer ' + userStore.token,
      },
      success: (uploadFileRes) => {
        const res = JSON.parse(uploadFileRes.data)
        if (res.code === 200) {
          resolve(res)
        } else {
          reject(res)
        }
      },
      fail: (err) => {
        reject(err)
      },
    })
  })
}
