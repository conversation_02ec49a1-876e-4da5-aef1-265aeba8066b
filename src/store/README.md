# 用户 Store 使用指南

## 🎯 特性
- ✅ 手动持久化存储（兼容 uni-app）
- ✅ 简洁的用户认证状态管理
- ✅ Token 自动过期检查
- ✅ 完全自定义的数据同步

## 📱 手动持久化原理

### 存储机制
```javascript
// 保存数据
uni.setStorageSync('user-store', JSON.stringify({
  userInfo: { ... },
  token: 'xxx',
  expiresTime: 1672531200000,
  isLoggedIn: true
}))

// 读取数据
const data = uni.getStorageSync('user-store')
const parsedData = JSON.parse(data)
```

### 自动触发时机
- `setLoginInfo()` - 设置登录信息后自动保存
- `setUserInfo()` - 设置用户信息后自动保存
- `logout()` - 登出时自动清除存储
- `initUserState()` - 应用启动时自动加载

## 🚀 基本使用

### 在页面中使用
```javascript
import { useUserStore } from '@/store/user'
import { computed } from 'vue'

export default {
  setup() {
    const userStore = useUserStore()
    
    // 获取用户信息
    const userInfo = computed(() => userStore.userInfo)
    const isAuthenticated = computed(() => userStore.isAuthenticated)
    
    return {
      userInfo,
      isAuthenticated
    }
  }
}
```

### 登录功能
```javascript
const handleLogin = async (loginForm) => {
  try {
    await userStore.login({
      phone: loginForm.phone,
      code: loginForm.code
    })
    
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: error.message || '登录失败',
      icon: 'none'
    })
  }
}
```

### 登出功能
```javascript
const handleLogout = async () => {
  try {
    await userStore.logout()
    // 会自动跳转到登录页面
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}
```

## 📊 State 状态

- `userInfo`: 用户基本信息 `{ id, nickname, avatar, phone, email }`
- `token`: 访问令牌
- `expiresTime`: token过期时间
- `isLoggedIn`: 登录状态
- `loginLoading`: 登录加载状态

## 🔍 Getters 计算属性

- `isAuthenticated`: 是否已认证（登录且token未过期）

## ⚡ Actions 方法

### 公开方法
- `setLoginInfo(loginData)`: 设置登录信息
- `setUserInfo(userInfo)`: 设置用户信息
- `login(loginParams)`: 登录（需要接入API）
- `logout()`: 登出
- `refreshUserInfo()`: 刷新用户信息（需要接入API）
- `initUserState()`: 初始化用户状态

### 私有方法（内部使用）
- `_saveToStorage()`: 保存数据到本地存储
- `_loadFromStorage()`: 从本地存储加载数据
- `_clearStorage()`: 清除本地存储

## 🔧 应用启动时初始化

在 `App.vue` 的 `onLaunch` 中调用：

```javascript
import { useUserStore } from '@/store/user'

export default {
  onLaunch() {
    const userStore = useUserStore()
    // 初始化用户状态，会自动从本地存储恢复数据
    userStore.initUserState()
  }
}
```

## ⚠️ 注意事项

1. **API 接入**: 目前 `login`、`refreshUserInfo` 方法需要接入实际的API
2. **数据同步**: 每次状态变更都会自动保存到本地存储
3. **错误处理**: 存储操作失败会在控制台输出错误信息
4. **兼容性**: 完全基于 uni-app 的存储API，支持所有平台
