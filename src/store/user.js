import { defineStore } from 'pinia'
import { getProfile } from '@/api/user'
import { wxLogin } from '@/api/auth'

// 持久化存储的 key
const STORAGE_KEY = 'user-store'

// 需要持久化的字段
const PERSIST_FIELDS = ['userInfo', 'token', 'expiresTime', 'isLoggedIn']

export const useUserStore = defineStore('user', {
  state: () => ({
    // 用户基本信息
    userInfo: null,
    // 认证信息
    token: '',
    expiresTime: null,
    // 登录状态
    isLoggedIn: false,
    // 加载状态
    loginLoading: false,
  }),

  getters: {
    // 检查是否已认证（登录且token未过期）
    isAuthenticated: (state) => {
      if (!state.isLoggedIn || !state.token) return false
      if (!state.expiresTime) return false
      return Date.now() < state.expiresTime
    },

    // 获取完整的用户信息对象
    getUserInfo: (state) => state.userInfo,

    // 获取用户昵称
    nickname: (state) => state.userInfo?.nickname || '',

    // 获取用户头像
    avatar: (state) => state.userInfo?.avatar || '',

    // 获取用户手机号
    phone: (state) => state.userInfo?.phonenumber || '',
  },

  actions: {
    // 手动持久化：保存数据到本地存储
    _saveToStorage() {
      try {
        const data = {}
        PERSIST_FIELDS.forEach(field => {
          data[field] = this[field]
        })
        uni.setStorageSync(STORAGE_KEY, JSON.stringify(data))
      } catch (error) {
        console.error('保存用户数据失败:', error)
      }
    },

    // 手动持久化：从本地存储加载数据
    _loadFromStorage() {
      try {
        const data = uni.getStorageSync(STORAGE_KEY)
        if (data) {
          const parsedData = JSON.parse(data)
          PERSIST_FIELDS.forEach(field => {
            if (parsedData[field] !== undefined) {
              this[field] = parsedData[field]
            }
          })
        }
      } catch (error) {
        console.error('加载用户数据失败:', error)
      }
    },

    // 手动持久化：清除本地存储
    _clearStorage() {
      try {
        uni.removeStorageSync(STORAGE_KEY)
      } catch (error) {
        console.error('清除用户数据失败:', error)
      }
    },

    // 设置登录信息
    setLoginInfo(loginData) {
      const { token, expiresTime } = loginData

      this.token = token
      this.expiresTime = expiresTime
      this.isLoggedIn = true

      // 保存到本地存储
      this._saveToStorage()
    },

    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = userInfo

      // 保存到本地存储
      this._saveToStorage()
    },

    // 静默登录
    async login() {
      this.loginLoading = true
      try {
        const code = await new Promise((resolve, reject) => {
          uni.login({
            provider: 'weixin',
            success: (res) => resolve(res.code),
            fail: (err) => reject(err),
          })
        })
        const { data: loginRes } = await wxLogin(code)
        this.setLoginInfo(loginRes.data)
        return Promise.resolve()
      } catch (error) {
        console.error('登录失败:', error)
        return Promise.reject(error)
      } finally {
        this.loginLoading = false
      }
    },

    // 登出方法
    logout() {
      this.$reset()
      // 清除本地存储
      this._clearStorage()
      console.log('已退出登录，并已清除本地状态')
      uni.reLaunch({
        url: '/pages/tower/tower',
      })
    },

    // 刷新用户信息
    async refreshUserInfo() {
      try {
        const response = await getProfile()
        this.setUserInfo(response.data)
        return Promise.resolve(response.data)
      } catch (error) {
        console.error('刷新用户信息失败:', error)
        return Promise.reject(error)
      }
    },
  },
})
