<template>
  <view class="empty">
    <image class="empty-image" :src="imageUrl" mode="aspectFit" />
    <text class="empty-title">{{ title }}</text>
    <text class="empty-description" v-if="description">{{ description }}</text>
    <view class="action-btn" v-if="buttonText" @click="handleButtonClick">
      <text class="action-btn-text">{{ buttonText }}</text>
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(['button-click'])

defineProps({
  imageUrl: {
    type: String,
    default: '/static/no-order.png'
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  buttonText: {
    type: String,
    default: ''
  }
})

const handleButtonClick = () => {
  emit('button-click')
}
</script>

<style lang="scss" scoped>
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;

  .empty-image {
    width: 400rpx;
    height: 400rpx;
    margin-bottom: 40rpx;
  }

  .empty-title {
    font-size: 32rpx;
    line-height: 42rpx;
    color: #1a1a1a;
    font-weight: 500;
    margin-bottom: 16rpx;
  }

  .empty-description {
    font-size: 28rpx;
    line-height: 33rpx;
    color: #767676;
    margin-bottom: 40rpx;
  }

  .action-btn {
    width: 400rpx;
    height: 88rpx;
    background-color: #ffffff;
    border: 2rpx solid rgba(221, 60, 41, 0.5);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      opacity: 0.8;
    }
  }

  .action-btn-text {
    font-size: 32rpx;
    line-height: 38rpx;
    color: #dd3c29;
  }
}
</style>
