<script setup>
defineProps({
  text: {
    type: String,
    required: true,
  },
  active: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['click']);

function handleClick() {
  emit('click');
}
</script>

<template>
  <view class="tab-container" @click="handleClick">
    <text class="tab-text" :class="{ 'is-active': active }">{{ text }}</text>
    <view class="active-indicator" v-if="active"></view>
  </view>
</template>

<style lang="scss" scoped>
.tab-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 96rpx; // Match the container height
  padding: 0 20rpx;
}

.tab-text {
  font-size: 32rpx;
  font-weight: 400;
  line-height: 38rpx;
  color: #666666;
  white-space: pre;

  &.is-active {
    font-weight: 500;
    color: #000000;
  }
}

.active-indicator {
  width: 32rpx;
  height: 6rpx;
  background-color: #dd3c29;
  border-radius: 3rpx;
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
}
</style>