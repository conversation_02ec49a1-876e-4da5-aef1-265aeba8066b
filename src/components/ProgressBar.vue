<template>
  <view class="progress-bar-container">
    <view class="progress-bar-bg">
      <view class="progress-bar-fill" :style="{ width: percentage + '%' }">
        <image class="progress-texture" src="/static/images/progress-texture.png" mode="widthFix" />
      </view>
    </view>
    <view class="progress-label" :style="{ left: Math.max(5, Math.min(95, percentage)) + '%' }">
      <text class="progress-text">{{ percentage }}%</text>
    </view>
  </view>
</template>

<script setup>
defineProps({
  percentage: {
    type: Number,
    required: true,
    default: 0
  }
})
</script>

<style lang="scss" scoped>
.progress-bar-container {
  position: relative;
  width: 100%;
  height: 40rpx;
  display: flex;
  align-items: center;
}

.progress-bar-bg {
  width: 100%;
  height: 22rpx;
  background-color: #f1edec;
  border-radius: 23rpx;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: #dd3c29;
  border-radius: 23rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: width 0.3s ease;
}

.progress-texture {
  width: 100%;
  height: 100%;
  opacity: 0.5;
}

.progress-label {
  position: absolute;
  transform: translateX(-50%);
  width: 79rpx;
  height: 40rpx;
  background-color: #dd3c29;
  border: 2rpx solid #ffffff;
  border-radius: 57rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: left 0.3s ease;
}

.progress-text {
  font-size: 26rpx;
  font-weight: 700;
  line-height: 30rpx;
  color: #ffffff;
}
</style>
