<template>
  <view class="custom-nav-bar" :style="navBarStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 导航栏内容 -->
    <view class="nav-content">
      <!-- 左侧返回按钮 -->
      <view 
        v-if="showBack" 
        class="back-button" 
        @click="handleBack"
      >
        <image
          class="back-icon"
          src="/static/icons/back.svg"
          mode="aspectFit"
        />
      </view>
      
      <!-- 中间标题 -->
      <view class="title-container">
        <text class="title-text">{{ title }}</text>
      </view>
      
      <!-- 右侧占位，保持居中 -->
      <view class="right-placeholder" v-if="showBack"></view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// Props 定义
const props = defineProps({
  // 标题文本
  title: {
    type: String,
    default: ''
  },
  // 是否显示返回按钮
  showBack: {
    type: Boolean,
    default: true
  },
  // 背景色（支持透明）
  backgroundColor: {
    type: String,
    default: 'transparent'
  },
  // 标题颜色
  titleColor: {
    type: String,
    default: '#000000'
  }
})

// Emits 定义
const emit = defineEmits(['back'])

// 响应式数据
const statusBarHeight = ref(0)
const navBarHeight = ref(44) // 导航栏内容高度，一般为44px

// 计算导航栏样式
const navBarStyle = computed(() => ({
  backgroundColor: props.backgroundColor
}))

// 获取系统信息
const getSystemInfo = () => {
  try {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight || 0
    
    // 根据平台调整导航栏高度
    if (systemInfo.platform === 'ios') {
      navBarHeight.value = 44
    } else {
      navBarHeight.value = 48
    }
  } catch (error) {
    console.error('获取系统信息失败:', error)
    // 设置默认值
    statusBarHeight.value = 20
    navBarHeight.value = 44
  }
}

// 处理返回按钮点击
const handleBack = () => {
  emit('back')
  // 默认行为：返回上一页
  uni.navigateBack({
    delta: 1
  })
}

// 组件挂载时获取系统信息
onMounted(() => {
  getSystemInfo()
})

// 暴露总高度给父组件使用
defineExpose({
  totalHeight: computed(() => statusBarHeight.value + navBarHeight.value)
})
</script>

<style lang="scss" scoped>
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  width: 100%;
}

.status-bar {
  width: 100%;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16rpx;
  position: relative;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  
  &:active {
    background-color: rgba(0, 0, 0, 0.2);
    transform: scale(0.95);
    transition: all 0.1s ease;
  }
}

.back-icon {
  width: 32rpx;
  height: 32rpx;
}

.title-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  max-width: 60%;
}

.title-text {
  font-size: 36rpx;
  font-weight: 500;
  color: v-bind('props.titleColor');
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.right-placeholder {
  width: 60rpx;
  height: 60rpx;
}
</style>
