<template>
  <view class="adoption-card">
    <view class="card-header">
      <image class="product-image" :src="adoption.fruitImage || '/static/images/default-fruit.png'" mode="aspectFill" />
      <view class="product-info">
        <text class="product-title">{{ adoption.fruitTreeName }}</text>
        <text class="product-specs">{{ adoption.fruitDesc }}</text>
      </view>
    </view>

    <view class="card-body">
      <ProgressBar :percentage="progressPercentage" />

      <view class="date-info">
        <text class="date-text">{{ adoption.startDate }} 购买</text>
        <text class="date-text">{{ adoption.endDate }} 到期</text>
      </view>

      <view class="certificate-info">
        <view class="buttons">
          <view class="equity-btn" @click="handleEquityClick">
            <text class="equity-btn-text">我的权益</text>
          </view>
          <view class="view-certificate-btn" @click="handleCertificateClick">
            <text class="btn-text">查看证书</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import ProgressBar from '@/components/ProgressBar.vue'

const emit = defineEmits(['navigate-to-equity', 'show-certificate'])

const props = defineProps({
  adoption: {
    type: Object,
    required: true
  }
})

// 计算进度百分比（基于开始和结束日期）
const progressPercentage = computed(() => {
  const startDate = new Date(props.adoption.startDate)
  const endDate = new Date(props.adoption.endDate)
  const currentDate = new Date()

  if (currentDate < startDate) return 0
  if (currentDate > endDate) return 100

  const totalDays = (endDate - startDate) / (1000 * 60 * 60 * 24)
  const passedDays = (currentDate - startDate) / (1000 * 60 * 60 * 24)

  return Math.round((passedDays / totalDays) * 100)
})

const handleEquityClick = () => {
  emit('navigate-to-equity', props.adoption)
}

const handleCertificateClick = () => {
  emit('show-certificate', props.adoption)
}
</script>

<style lang="scss" scoped>
.adoption-card {
  width: 686rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.product-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.product-info {
  display: flex;
  flex-direction: column;
  margin-left: 24rpx;
  padding-top: 10rpx;
}

.product-title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #1a1a1a;
}

.product-specs {
  font-size: 30rpx;
  line-height: 40rpx;
  color: #999999;
  margin-top: 16rpx;
}

.card-body {
  margin-top: 32rpx;
  width: 100%;
}

.date-info {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
}

.date-text {
  font-size: 24rpx;
  line-height: 28rpx;
  color: #1a1a1a;
}

.certificate-info {
  margin-top: 24rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}



.buttons {
  display: flex;
  gap: 16rpx;
}

.equity-btn {
  width: 166rpx;
  height: 64rpx;
  background-color: #ffffff;
  border: 1rpx solid #dd3c29;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    opacity: 0.8;
  }
}

.equity-btn-text {
  font-size: 30rpx;
  line-height: 35rpx;
  color: #dd3c29;
}

.view-certificate-btn {
  width: 166rpx;
  height: 64rpx;
  background-color: #dd3c29;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    opacity: 0.8;
  }
}

.btn-text {
  font-size: 30rpx;
  line-height: 35rpx;
  color: #ffffff;
}
</style>
