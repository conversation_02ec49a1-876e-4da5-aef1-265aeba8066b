<template>
  <view class="address-card" @click="handleClick">
    <!-- 用户信息 -->
    <view class="user-info">
      <text class="user-name">{{ address.contactName }}</text>
      <text class="user-phone">{{ address.phone }}</text>
    </view>

    <!-- 地址信息 -->
    <text class="address-text">{{ `${address.province}${address.city}${address.district}${address.detailAddress}` }}</text>

    <!-- 分割线 -->
    <view class="divider"></view>

    <!-- 操作区域 -->
    <view class="action-row">
      <!-- 默认地址标识 -->
      <view class="default-section" @click.stop="handleSetDefault">
        <image
          class="default-icon"
          :src="address.isDefault ? '/static/icons/checked.svg' : '/static/icons/unchecked.svg'"
          mode="aspectFit"
        />
        <text class="default-text">默认寄件地址</text>
      </view>

      <!-- 编辑按钮 -->
      <view class="action-button" @click="handleEdit">
        <image class="action-icon" src="/static/edit.svg" mode="aspectFit" />
        <text class="action-text">编辑</text>
      </view>

      <!-- 删除按钮 -->
      <view class="action-button" @click="handleDelete">
        <image class="action-icon" src="/static/delete.svg" mode="aspectFit" />
        <text class="action-text">删除</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// 定义props
const props = defineProps({
  address: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      contactName: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detailedAddress: '',
      isDefault: false
    })
  }
})

// 定义事件
const emit = defineEmits(['edit', 'delete', 'set-default', 'click'])

// 编辑地址
const handleEdit = () => {
  emit('edit', props.address)
}

// 删除地址
const handleDelete = () => {
  emit('delete', props.address)
}

// 设置默认地址
const handleSetDefault = () => {
  if (!props.address.isDefault) {
    emit('set-default', props.address)
  }
}

const handleClick = () => {
  emit('click', props.address)
}
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-primary: #1a1a1a;
$text-secondary: #767676;
$border-color: #d8d8d8;
$card-radius: 20rpx;

// 混入
@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.address-card {
  background-color: $white-color;
  border-radius: $card-radius;
  padding: 32rpx 40rpx; // 增加左右内边距
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 32rpx;
}

.user-info {
  @include flex-between;
  margin-bottom: 24rpx;

  .user-name {
    font-size: 32rpx;
    line-height: 38rpx;
    color: $text-primary;
    font-weight: 400;
  }

  .user-phone {
    font-size: 28rpx;
    line-height: 33rpx;
    color: $text-secondary;
  }
}

.address-text {
  display: block;
  font-size: 28rpx;
  line-height: 33rpx;
  color: $text-secondary;
  margin-bottom: 24rpx;
  padding-right: 20rpx; // 避免文字贴边
}

.divider {
  width: 100%; // 分割线不延伸到卡片边缘
  height: 1rpx;
  background-color: #d8d8d8; // 使用设计图指定的颜色
  margin: 0 0 24rpx 0; // 移除负边距
}

.action-row {
  @include flex-between;
  
  .default-section {
    display: flex;
    align-items: center;
    
    .default-icon {
      width: 28rpx;
      height: 28rpx;
      margin-right: 12rpx;
    }
    
    .default-text {
      font-size: 28rpx;
      line-height: 33rpx;
      color: $text-secondary;
    }
  }
  
  .action-button {
    display: flex;
    align-items: center;
    margin-left: 32rpx;
    
    &:active {
      opacity: 0.6;
    }
    
    .action-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }
    
    .action-text {
      font-size: 28rpx;
      line-height: 33rpx;
      color: $text-secondary;
    }
  }
}
</style>
